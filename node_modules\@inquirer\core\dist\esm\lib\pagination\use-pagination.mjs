import { useRef } from '../use-ref.mjs';
import { readlineWidth } from '../utils.mjs';
import { lines } from './lines.mjs';
import { finite, infinite } from './position.mjs';
export function usePagination({ items, active, renderItem, pageSize, loop = true, }) {
    const state = useRef({ position: 0, lastActive: 0 });
    const position = loop
        ? infinite({
            active,
            lastActive: state.current.lastActive,
            total: items.length,
            pageSize,
            pointer: state.current.position,
        })
        : finite({
            active,
            total: items.length,
            pageSize,
        });
    state.current.position = position;
    state.current.lastActive = active;
    return lines({
        items,
        width: readlineWidth(),
        renderItem,
        active,
        position,
        pageSize,
    }).join('\n');
}
