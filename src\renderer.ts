/**
 * <PERSON><PERSON><PERSON> Print Server - Renderer Process
 * Main UI logic for the print server application
 */

import "./index.css";

// Type definitions for our API
interface Printer {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  status?: string;
  isDefault?: boolean;
}

interface Invoice {
  id: string;
  invoiceNumber: string;
  data: any;
  status: "pending" | "printing" | "printed" | "failed";
  attempts: number;
  createdAt: Date;
  lastAttemptAt?: Date;
  printedAt?: Date;
  error?: string;
}

class PrintServerUI {
  private printers: Printer[] = [];
  private selectedPrinter: string | null = null;
  private isPolling = false;
  private refreshInterval: number | null = null;

  constructor() {
    this.initializeUI();
    this.setupEventListeners();
    this.loadInitialData();
  }

  private initializeUI(): void {
    console.log("🖨️ Kassierer Print Server UI initialized");
    this.updateStatus("Initializing...", "offline");
  }

  private setupEventListeners(): void {
    // Printer controls
    document
      .getElementById("refresh-printers")
      ?.addEventListener("click", () => {
        this.refreshPrinters();
      });

    document.getElementById("test-print")?.addEventListener("click", () => {
      this.testPrint();
    });

    document
      .getElementById("printer-select")
      ?.addEventListener("change", (e) => {
        const target = e.target as HTMLSelectElement;
        this.selectPrinter(target.value);
      });

    // Server controls
    document.getElementById("start-polling")?.addEventListener("click", () => {
      this.startPolling();
    });

    document.getElementById("stop-polling")?.addEventListener("click", () => {
      this.stopPolling();
    });

    // Queue controls
    document.getElementById("refresh-queue")?.addEventListener("click", () => {
      this.refreshQueue();
    });

    document.getElementById("retry-failed")?.addEventListener("click", () => {
      this.retryFailedInvoices();
    });

    document.getElementById("clear-printed")?.addEventListener("click", () => {
      this.clearPrintedInvoices();
    });
  }

  private async loadInitialData(): Promise<void> {
    try {
      await this.refreshPrinters();
      await this.loadSelectedPrinter();
      await this.refreshQueue();
      await this.updateServerStatus();

      this.updateStatus("Ready", "online");

      // Start auto-refresh
      this.startAutoRefresh();
    } catch (error) {
      console.error("Failed to load initial data:", error);
      this.updateStatus("Error", "offline");
      this.showError("Failed to initialize application");
    }
  }

  private async refreshPrinters(): Promise<void> {
    try {
      this.setLoading("refresh-printers", true);

      const printers = await window.electronAPI.getPrinters();
      this.printers = printers;
      this.updatePrinterSelect();

      console.log(`Found ${printers.length} printers`);
    } catch (error) {
      console.error("Failed to refresh printers:", error);
      this.showError("Failed to discover printers");
    } finally {
      this.setLoading("refresh-printers", false);
    }
  }

  private updatePrinterSelect(): void {
    const select = document.getElementById(
      "printer-select"
    ) as HTMLSelectElement;
    if (!select) return;

    select.innerHTML = "";

    if (this.printers.length === 0) {
      select.innerHTML = '<option value="">No printers found</option>';
      return;
    }

    select.innerHTML = '<option value="">Select a printer...</option>';

    this.printers.forEach((printer) => {
      const option = document.createElement("option");
      option.value = printer.id;
      option.textContent = `${printer.displayName}${printer.isDefault ? " (Default)" : ""}`;
      select.appendChild(option);
    });

    // Set selected printer if we have one
    if (this.selectedPrinter) {
      select.value = this.selectedPrinter;
    }
  }

  private async loadSelectedPrinter(): Promise<void> {
    try {
      const selectedPrinter = await window.electronAPI.getSelectedPrinter();
      this.selectedPrinter = selectedPrinter;

      if (selectedPrinter) {
        this.updateSelectedPrinterInfo();
        this.enableTestPrint();
      }
    } catch (error) {
      console.error("Failed to load selected printer:", error);
    }
  }

  private async selectPrinter(printerId: string): Promise<void> {
    if (!printerId) {
      this.selectedPrinter = null;
      this.hideSelectedPrinterInfo();
      this.disableTestPrint();
      return;
    }

    try {
      const success = await window.electronAPI.selectPrinter(printerId);

      if (success) {
        this.selectedPrinter = printerId;
        this.updateSelectedPrinterInfo();
        this.enableTestPrint();
        this.showSuccess("Printer selected successfully");
      } else {
        this.showError("Failed to select printer");
      }
    } catch (error) {
      console.error("Failed to select printer:", error);
      this.showError("Failed to select printer");
    }
  }

  private updateSelectedPrinterInfo(): void {
    const infoDiv = document.getElementById("selected-printer-info");
    const detailsDiv = document.getElementById("printer-details");

    if (!infoDiv || !detailsDiv || !this.selectedPrinter) return;

    const printer = this.printers.find((p) => p.id === this.selectedPrinter);
    if (!printer) return;

    detailsDiv.innerHTML = `
      <p><strong>Name:</strong> ${printer.displayName}</p>
      <p><strong>ID:</strong> ${printer.id}</p>
      ${printer.description ? `<p><strong>Description:</strong> ${printer.description}</p>` : ""}
      ${printer.status ? `<p><strong>Status:</strong> ${printer.status}</p>` : ""}
      ${printer.isDefault ? "<p><strong>Default Printer:</strong> Yes</p>" : ""}
    `;

    infoDiv.style.display = "block";
  }

  private hideSelectedPrinterInfo(): void {
    const infoDiv = document.getElementById("selected-printer-info");
    if (infoDiv) {
      infoDiv.style.display = "none";
    }
  }

  private enableTestPrint(): void {
    const testBtn = document.getElementById("test-print") as HTMLButtonElement;
    if (testBtn) {
      testBtn.disabled = false;
    }
  }

  private disableTestPrint(): void {
    const testBtn = document.getElementById("test-print") as HTMLButtonElement;
    if (testBtn) {
      testBtn.disabled = true;
    }
  }

  private async testPrint(): Promise<void> {
    if (!this.selectedPrinter) {
      this.showError("No printer selected");
      return;
    }

    try {
      this.setLoading("test-print", true);

      // For now, we'll just show a success message
      // In a real implementation, this would call a test print method
      await new Promise((resolve) => setTimeout(resolve, 2000)); // Simulate printing

      this.showSuccess("Test print sent successfully");
    } catch (error) {
      console.error("Test print failed:", error);
      this.showError("Test print failed");
    } finally {
      this.setLoading("test-print", false);
    }
  }

  private async startPolling(): Promise<void> {
    if (!this.selectedPrinter) {
      this.showError("Please select a printer first");
      return;
    }

    try {
      this.setLoading("start-polling", true);

      const success = await window.electronAPI.startInvoicePolling();

      if (success) {
        this.isPolling = true;
        this.updatePollingControls();
        this.showSuccess("Invoice polling started");
        this.updateStatus("Polling for invoices...", "online");
      } else {
        this.showError("Failed to start polling");
      }
    } catch (error) {
      console.error("Failed to start polling:", error);
      this.showError("Failed to start polling");
    } finally {
      this.setLoading("start-polling", false);
    }
  }

  private async stopPolling(): Promise<void> {
    try {
      this.setLoading("stop-polling", true);

      const success = await window.electronAPI.stopInvoicePolling();

      if (success) {
        this.isPolling = false;
        this.updatePollingControls();
        this.showSuccess("Invoice polling stopped");
        this.updateStatus("Ready", "online");
      } else {
        this.showError("Failed to stop polling");
      }
    } catch (error) {
      console.error("Failed to stop polling:", error);
      this.showError("Failed to stop polling");
    } finally {
      this.setLoading("stop-polling", false);
    }
  }

  private updatePollingControls(): void {
    const startBtn = document.getElementById(
      "start-polling"
    ) as HTMLButtonElement;
    const stopBtn = document.getElementById(
      "stop-polling"
    ) as HTMLButtonElement;
    const statusSpan = document.getElementById("polling-status");

    if (startBtn) startBtn.disabled = this.isPolling;
    if (stopBtn) stopBtn.disabled = !this.isPolling;
    if (statusSpan)
      statusSpan.textContent = this.isPolling ? "Running" : "Stopped";
  }

  private async refreshQueue(): Promise<void> {
    try {
      this.setLoading("refresh-queue", true);

      const queue = await window.electronAPI.getInvoiceQueue();
      this.updateQueueDisplay(queue);

      // Update stats
      await this.updateServerStatus();
    } catch (error) {
      console.error("Failed to refresh queue:", error);
      this.showError("Failed to refresh queue");
    } finally {
      this.setLoading("refresh-queue", false);
    }
  }

  private updateQueueDisplay(invoices: Invoice[]): void {
    const queueList = document.getElementById("queue-list");
    if (!queueList) return;

    if (invoices.length === 0) {
      queueList.innerHTML = '<p class="empty-state">No invoices in queue</p>';
      return;
    }

    queueList.innerHTML = invoices
      .map(
        (invoice) => `
      <div class="queue-item">
        <div class="queue-item-info">
          <div class="queue-item-title">${invoice.invoiceNumber}</div>
          <div class="queue-item-meta">
            Created: ${new Date(invoice.createdAt).toLocaleString()}
            ${invoice.attempts > 0 ? `• Attempts: ${invoice.attempts}` : ""}
            ${invoice.error ? `• Error: ${invoice.error}` : ""}
          </div>
        </div>
        <div class="queue-item-status status-${invoice.status}">
          ${invoice.status}
        </div>
      </div>
    `
      )
      .join("");
  }

  private async retryFailedInvoices(): Promise<void> {
    try {
      this.setLoading("retry-failed", true);

      const success = await window.electronAPI.retryFailedInvoices();

      if (success) {
        this.showSuccess("Failed invoices queued for retry");
        await this.refreshQueue();
      } else {
        this.showError("Failed to retry invoices");
      }
    } catch (error) {
      console.error("Failed to retry invoices:", error);
      this.showError("Failed to retry invoices");
    } finally {
      this.setLoading("retry-failed", false);
    }
  }

  private async clearPrintedInvoices(): Promise<void> {
    // This would need to be implemented in the backend
    this.showInfo("Clear printed invoices feature coming soon");
  }

  private async updateServerStatus(): Promise<void> {
    try {
      const status = await window.electronAPI.getPrintServerStatus();

      // Update queue stats
      const queueLengthSpan = document.getElementById("queue-length");
      const totalPrintedSpan = document.getElementById("total-printed");
      const totalFailedSpan = document.getElementById("total-failed");

      if (queueLengthSpan)
        queueLengthSpan.textContent = status.queueLength.toString();
      if (totalPrintedSpan)
        totalPrintedSpan.textContent =
          status.invoiceStatus?.queueStats?.printed?.toString() || "0";
      if (totalFailedSpan)
        totalFailedSpan.textContent =
          status.invoiceStatus?.queueStats?.failed?.toString() || "0";

      // Update polling status
      this.isPolling = status.invoiceStatus?.isPolling || false;
      this.updatePollingControls();
    } catch (error) {
      console.error("Failed to update server status:", error);
    }
  }

  private startAutoRefresh(): void {
    // Refresh status every 5 seconds
    this.refreshInterval = window.setInterval(() => {
      this.updateServerStatus();
    }, 5000);
  }

  private stopAutoRefresh(): void {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
    }
  }

  // UI utility methods
  private updateStatus(
    text: string,
    type: "online" | "offline" | "warning"
  ): void {
    const statusText = document.getElementById("status-text");
    const statusDot = document.getElementById("status-dot");

    if (statusText) statusText.textContent = text;
    if (statusDot) {
      statusDot.className = `status-dot ${type}`;
    }
  }

  private setLoading(buttonId: string, loading: boolean): void {
    const button = document.getElementById(buttonId) as HTMLButtonElement;
    if (!button) return;

    if (loading) {
      button.disabled = true;
      button.classList.add("loading");
    } else {
      button.disabled = false;
      button.classList.remove("loading");
    }
  }

  private showMessage(
    message: string,
    type: "error" | "success" | "info"
  ): void {
    // Remove existing messages
    document
      .querySelectorAll(".error-message, .success-message, .info-message")
      .forEach((el) => el.remove());

    const messageDiv = document.createElement("div");
    messageDiv.className = `${type}-message`;
    messageDiv.textContent = message;

    // Add to the first card
    const firstCard = document.querySelector(".card");
    if (firstCard) {
      firstCard.insertBefore(messageDiv, firstCard.firstChild);
    }

    // Auto-remove after 5 seconds
    setTimeout(() => {
      messageDiv.remove();
    }, 5000);
  }

  private showError(message: string): void {
    this.showMessage(message, "error");
  }

  private showSuccess(message: string): void {
    this.showMessage(message, "success");
  }

  private showInfo(message: string): void {
    this.showMessage(message, "info");
  }
}

// Initialize the UI when the DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  new PrintServerUI();
});
