{"name": "@inquirer/type", "version": "1.5.5", "description": "Inquirer core TS types", "main": "./dist/cjs/index.js", "typings": "./dist/cjs/types/index.d.ts", "files": ["dist/**/*"], "repository": {"type": "git", "url": "https://github.com/SBoudrias/Inquirer.js.git"}, "keywords": ["answer", "answers", "ask", "base", "cli", "command", "command-line", "confirm", "enquirer", "generate", "generator", "hyper", "input", "inquire", "inquirer", "interface", "iterm", "javascript", "menu", "node", "nodejs", "prompt", "promptly", "prompts", "question", "readline", "scaffold", "scaffolder", "scaffolding", "stdin", "stdout", "terminal", "tty", "ui", "yeoman", "yo", "zsh", "types", "typescript"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"mute-stream": "^1.0.0"}, "scripts": {"tsc": "yarn run tsc:esm && yarn run tsc:cjs", "tsc:esm": "rm -rf dist/esm && tsc -p ./tsconfig.json", "tsc:cjs": "rm -rf dist/cjs && tsc -p ./tsconfig.cjs.json && node ../../tools/fix-ext.mjs", "attw": "attw --pack"}, "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/esm/types/index.d.mts", "default": "./dist/esm/index.mjs"}, "require": {"types": "./dist/cjs/types/index.d.ts", "default": "./dist/cjs/index.js"}}}, "sideEffects": false}