{"name": "@electron-forge/maker-zip", "version": "7.9.0", "description": "ZIP maker for Electron Forge", "repository": "https://github.com/electron/forge", "author": "<PERSON>", "license": "MIT", "main": "dist/MakerZIP.js", "typings": "dist/MakerZIP.d.ts", "devDependencies": {"@electron-forge/test-utils": "7.9.0", "vitest": "^3.1.3"}, "engines": {"node": ">= 16.4.0"}, "dependencies": {"@electron-forge/maker-base": "7.9.0", "@electron-forge/shared-types": "7.9.0", "cross-zip": "^4.0.0", "fs-extra": "^10.0.0", "got": "^11.8.5"}, "publishConfig": {"access": "public"}, "files": ["dist", "src"], "gitHead": "cd63f57bd6870af2ad847076a183456221b30269"}