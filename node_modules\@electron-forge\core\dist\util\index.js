"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_utils_1 = require("@electron-forge/core-utils");
const forge_config_1 = require("./forge-config");
class ForgeUtils {
    constructor() {
        this.getElectronVersion = core_utils_1.getElectronVersion;
        this.spawnPackageManager = core_utils_1.spawnPackageManager;
    }
    /**
     * Helper for creating a dynamic config value that will get its real value
     * based on the "buildIdentifier" in your Forge config.
     *
     * Usage:
     * `fromBuildIdentifier({ stable: 'App', beta: 'App Beta' })`
     */
    fromBuildIdentifier(map) {
        return (0, forge_config_1.fromBuildIdentifier)(map);
    }
    /**
     * Register a virtual config file for forge to find.
     * Takes precedence over other configuration options like a forge.config.js file.
     * Dir should point to the folder containing the app.
     */
    registerForgeConfigForDirectory(dir, config) {
        return (0, forge_config_1.registerForgeConfigForDirectory)(dir, config);
    }
    /**
     * Unregister a forge config previously registered with registerForgeConfigForDirectory.
     */
    unregisterForgeConfigForDirectory(dir) {
        return (0, forge_config_1.unregisterForgeConfigForDirectory)(dir);
    }
}
exports.default = ForgeUtils;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi9zcmMvdXRpbC9pbmRleC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOztBQUFBLDJEQUdvQztBQUVwQyxpREFNd0I7QUFJeEIsTUFBcUIsVUFBVTtJQUEvQjtRQVlFLHVCQUFrQixHQUFHLCtCQUFrQixDQUFDO1FBRXhDLHdCQUFtQixHQUFHLGdDQUFtQixDQUFDO0lBaUI1QyxDQUFDO0lBOUJDOzs7Ozs7T0FNRztJQUNILG1CQUFtQixDQUFJLEdBQTBCO1FBQy9DLE9BQU8sSUFBQSxrQ0FBbUIsRUFBQyxHQUFHLENBQUMsQ0FBQztJQUNsQyxDQUFDO0lBTUQ7Ozs7T0FJRztJQUNILCtCQUErQixDQUFDLEdBQVcsRUFBRSxNQUFtQjtRQUM5RCxPQUFPLElBQUEsOENBQStCLEVBQUMsR0FBRyxFQUFFLE1BQU0sQ0FBQyxDQUFDO0lBQ3RELENBQUM7SUFFRDs7T0FFRztJQUNILGlDQUFpQyxDQUFDLEdBQVc7UUFDM0MsT0FBTyxJQUFBLGdEQUFpQyxFQUFDLEdBQUcsQ0FBQyxDQUFDO0lBQ2hELENBQUM7Q0FDRjtBQS9CRCw2QkErQkMifQ==