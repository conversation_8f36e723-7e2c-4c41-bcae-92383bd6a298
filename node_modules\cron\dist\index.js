"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateCronExpression = exports.timeout = exports.sendAt = exports.CronTime = exports.CronJob = void 0;
const time_1 = require("./time");
var job_1 = require("./job");
Object.defineProperty(exports, "CronJob", { enumerable: true, get: function () { return job_1.CronJob; } });
var time_2 = require("./time");
Object.defineProperty(exports, "CronTime", { enumerable: true, get: function () { return time_2.CronTime; } });
const sendAt = (cronTime) => new time_1.CronTime(cronTime).sendAt();
exports.sendAt = sendAt;
const timeout = (cronTime) => new time_1.CronTime(cronTime).getTimeout();
exports.timeout = timeout;
exports.validateCronExpression = time_1.CronTime.validateCronExpression;
