{"version": 3, "file": "upgrade-forge-config.d.ts", "sourceRoot": "", "sources": ["../../src/util/upgrade-forge-config.ts"], "names": [], "mappings": "AAEA,OAAO,EACL,WAAW,EACX,aAAa,EAGd,MAAM,8BAA8B,CAAC;AAMtC,KAAK,aAAa,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG;IAC7C,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;CAClC,CAAC;AAEF,KAAK,YAAY,GAAG;IAClB,YAAY,CAAC,EAAE,MAAM,CAAC,aAAa,EAAE,MAAM,EAAE,CAAC,CAAC;IAC/C,sBAAsB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACjD,qBAAqB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAChD,wBAAwB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACnD,oBAAoB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC/C,wBAAwB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACnD,uBAAuB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAClD,uBAAuB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAClD,qBAAqB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAChD,oBAAoB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC/C,kBAAkB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAE7C,iBAAiB,CAAC,EAAE,aAAa,CAAC;IAClC,EAAE,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC7B,yBAAyB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACpD,SAAS,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;CACrC,CAAC;AAIF,KAAK,gBAAgB,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG;IAChD,MAAM,EAAE;QACN,KAAK,EAAE,WAAW,CAAC;KACpB,CAAC;IACF,eAAe,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;CACzC,CAAC;AA+GF;;GAEG;AACH,MAAM,CAAC,OAAO,UAAU,kBAAkB,CACxC,YAAY,EAAE,YAAY,GACzB,WAAW,CAcb;AAED,wBAAgB,0BAA0B,CACxC,WAAW,EAAE,gBAAgB,EAC7B,OAAO,EAAE,MAAM,EAAE,GAChB,MAAM,EAAE,CAgBV"}