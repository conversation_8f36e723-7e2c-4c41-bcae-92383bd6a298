"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.findTemplate = void 0;
const debug_1 = __importDefault(require("debug"));
const global_dirs_1 = __importDefault(require("global-dirs"));
const d = (0, debug_1.default)('electron-forge:init:find-template');
var TemplateType;
(function (TemplateType) {
    TemplateType["global"] = "global";
    TemplateType["local"] = "local";
})(TemplateType || (TemplateType = {}));
const findTemplate = async (template) => {
    let foundTemplate = null;
    const resolveTemplateTypes = [
        [TemplateType.global, `electron-forge-template-${template}`],
        [TemplateType.global, `@electron-forge/template-${template}`],
        [TemplateType.local, `electron-forge-template-${template}`],
        [TemplateType.local, `@electron-forge/template-${template}`],
        [TemplateType.global, template],
        [TemplateType.local, template],
    ];
    for (const [templateType, moduleName] of resolveTemplateTypes) {
        try {
            d(`Trying ${templateType} template: ${moduleName}`);
            let templateModulePath;
            if (templateType === TemplateType.global) {
                templateModulePath = require.resolve(moduleName, {
                    paths: [global_dirs_1.default.npm.packages, global_dirs_1.default.yarn.packages],
                });
            }
            else {
                templateModulePath = require.resolve(moduleName);
            }
            foundTemplate = {
                path: templateModulePath,
                type: templateType,
                name: moduleName,
            };
            break;
        }
        catch (err) {
            d(`Error: ${err instanceof Error ? err.message : err}`);
        }
    }
    if (!foundTemplate) {
        throw new Error(`Failed to locate custom template: "${template}".`);
    }
    else {
        d(`found template module at: ${foundTemplate.path}`);
        const templateModule = await Promise.resolve(`${foundTemplate.path}`).then(s => __importStar(require(s)));
        const tmpl = templateModule.default ?? templateModule;
        return {
            ...foundTemplate,
            template: tmpl,
        };
    }
};
exports.findTemplate = findTemplate;
//# sourceMappingURL=data:application/json;base64,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