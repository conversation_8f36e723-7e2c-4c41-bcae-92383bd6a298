<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>Kassierer Print Server</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
  </head>
  <body>
    <div id="app">
      <header class="header">
        <h1>🖨️ Kassierer Print Server</h1>
        <div class="status-indicator" id="status-indicator">
          <span class="status-dot" id="status-dot"></span>
          <span id="status-text">Initializing...</span>
        </div>
      </header>

      <main class="main-content">
        <!-- Printer Setup Section -->
        <section class="card" id="printer-section">
          <h2>Printer Setup</h2>
          <div class="printer-controls">
            <button id="refresh-printers" class="btn btn-secondary">🔄 Refresh Printers</button>
            <button id="test-print" class="btn btn-secondary" disabled>🧪 Test Print</button>
          </div>

          <div class="printer-selection">
            <label for="printer-select">Select Printer:</label>
            <select id="printer-select" class="select-input">
              <option value="">Loading printers...</option>
            </select>
          </div>

          <div class="selected-printer" id="selected-printer-info" style="display: none;">
            <h3>Selected Printer</h3>
            <div id="printer-details"></div>
          </div>
        </section>

        <!-- Print Server Status Section -->
        <section class="card" id="server-section">
          <h2>Print Server Status</h2>
          <div class="server-controls">
            <button id="start-polling" class="btn btn-primary">▶️ Start Polling</button>
            <button id="stop-polling" class="btn btn-danger" disabled>⏹️ Stop Polling</button>
          </div>

          <div class="server-stats" id="server-stats">
            <div class="stat-item">
              <span class="stat-label">Status:</span>
              <span class="stat-value" id="polling-status">Stopped</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Queue Length:</span>
              <span class="stat-value" id="queue-length">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Total Printed:</span>
              <span class="stat-value" id="total-printed">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Failed:</span>
              <span class="stat-value" id="total-failed">0</span>
            </div>
          </div>
        </section>

        <!-- Invoice Queue Section -->
        <section class="card" id="queue-section">
          <h2>Invoice Queue</h2>
          <div class="queue-controls">
            <button id="refresh-queue" class="btn btn-secondary">🔄 Refresh</button>
            <button id="retry-failed" class="btn btn-warning">🔄 Retry Failed</button>
            <button id="clear-printed" class="btn btn-secondary">🗑️ Clear Printed</button>
          </div>

          <div class="queue-list" id="queue-list">
            <p class="empty-state">No invoices in queue</p>
          </div>
        </section>
      </main>
    </div>

    <script type="module" src="/src/renderer.ts"></script>
  </body>
</html>
