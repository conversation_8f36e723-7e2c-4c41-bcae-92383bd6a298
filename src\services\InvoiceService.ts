import axios from 'axios';
import { StorageService, Invoice } from './StorageService';
import { PrinterService } from './PrinterService';
import * as cron from 'cron';

export interface InvoiceApiResponse {
  invoices: any[];
  hasMore: boolean;
  lastId?: string;
}

export class InvoiceService {
  private storageService: StorageService;
  private printerService: PrinterService;
  private pollingJob: cron.CronJob | null = null;
  private retryJob: cron.CronJob | null = null;
  private isPolling = false;
  private isProcessing = false;

  constructor(storageService: StorageService, printerService: PrinterService) {
    this.storageService = storageService;
    this.printerService = printerService;
  }

  async startPolling(): Promise<boolean> {
    if (this.isPolling) {
      console.log('Polling already started');
      return true;
    }

    try {
      const settings = this.storageService.getSettings();
      
      // Create polling job - every minute by default
      this.pollingJob = new cron.CronJob(
        '*/1 * * * *', // Every minute
        () => this.pollForInvoices(),
        null,
        false,
        'Europe/Berlin'
      );

      // Create retry job - every 30 seconds
      this.retryJob = new cron.CronJob(
        '*/30 * * * * *', // Every 30 seconds
        () => this.processRetries(),
        null,
        false,
        'Europe/Berlin'
      );

      this.pollingJob.start();
      this.retryJob.start();
      this.isPolling = true;

      console.log('Invoice polling started');
      
      // Do an initial poll
      await this.pollForInvoices();
      
      return true;
    } catch (error) {
      console.error('Failed to start polling:', error);
      return false;
    }
  }

  async stopPolling(): Promise<boolean> {
    try {
      if (this.pollingJob) {
        this.pollingJob.stop();
        this.pollingJob = null;
      }

      if (this.retryJob) {
        this.retryJob.stop();
        this.retryJob = null;
      }

      this.isPolling = false;
      console.log('Invoice polling stopped');
      return true;
    } catch (error) {
      console.error('Failed to stop polling:', error);
      return false;
    }
  }

  private async pollForInvoices(): Promise<void> {
    if (this.isProcessing) {
      console.log('Already processing invoices, skipping poll');
      return;
    }

    try {
      this.isProcessing = true;
      console.log('Polling for new invoices...');

      const settings = this.storageService.getSettings();
      const response = await this.fetchInvoicesFromAPI(settings.apiEndpoint);

      if (response.invoices && response.invoices.length > 0) {
        console.log(`Found ${response.invoices.length} new invoices`);
        
        for (const invoiceData of response.invoices) {
          await this.processNewInvoice(invoiceData);
        }
      } else {
        console.log('No new invoices found');
      }
    } catch (error) {
      console.error('Error polling for invoices:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  private async fetchInvoicesFromAPI(apiEndpoint: string): Promise<InvoiceApiResponse> {
    try {
      // For now, return mock data
      // In production, this would make a real API call
      const mockInvoices = this.generateMockInvoices();
      
      return {
        invoices: mockInvoices,
        hasMore: false
      };
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  private generateMockInvoices(): any[] {
    // Generate mock invoice data occasionally for testing
    const shouldGenerateMock = Math.random() < 0.1; // 10% chance
    
    if (!shouldGenerateMock) {
      return [];
    }

    return [{
      invoiceNumber: `INV-${Date.now()}`,
      customerName: 'Test Customer',
      amount: 99.99,
      items: [
        { name: 'Test Item', quantity: 1, price: 99.99 }
      ],
      date: new Date().toISOString()
    }];
  }

  private async processNewInvoice(invoiceData: any): Promise<void> {
    try {
      // Check if invoice already exists to prevent duplicates
      if (this.storageService.invoiceExists(invoiceData.invoiceNumber)) {
        console.log(`Invoice ${invoiceData.invoiceNumber} already exists, skipping`);
        return;
      }

      // Add invoice to storage
      const invoice = this.storageService.addInvoice(invoiceData);
      console.log(`Added new invoice to queue: ${invoice.invoiceNumber}`);

      // Try to print immediately
      await this.printInvoice(invoice);
    } catch (error) {
      console.error('Error processing new invoice:', error);
    }
  }

  private async printInvoice(invoice: Invoice): Promise<void> {
    try {
      // Check if printer is selected
      const selectedPrinter = await this.printerService.getSelectedPrinter();
      if (!selectedPrinter) {
        throw new Error('No printer selected');
      }

      // Mark as printing
      this.storageService.markInvoiceAsPrinting(invoice.id);
      console.log(`Printing invoice ${invoice.invoiceNumber}...`);

      // Print the invoice
      await this.printerService.printInvoice(invoice);

      // Mark as printed
      this.storageService.markInvoiceAsPrinted(invoice.id);
      console.log(`Successfully printed invoice ${invoice.invoiceNumber}`);

      // Emit event for UI updates
      this.emitInvoicePrinted(invoice);
    } catch (error) {
      console.error(`Failed to print invoice ${invoice.invoiceNumber}:`, error);
      
      // Mark as failed
      this.storageService.markInvoiceAsFailed(invoice.id, error.message);
      
      // Emit event for UI updates
      this.emitInvoiceFailed(invoice, error.message);
    }
  }

  private async processRetries(): Promise<void> {
    if (this.isProcessing) {
      return;
    }

    try {
      const failedInvoices = this.storageService.getFailedInvoices();
      const pendingInvoices = this.storageService.getPendingInvoices();
      
      // Process pending invoices first
      for (const invoice of pendingInvoices) {
        if (invoice.attempts > 0) {
          // Wait for retry delay
          const settings = this.storageService.getSettings();
          const timeSinceLastAttempt = Date.now() - (invoice.lastAttemptAt?.getTime() || 0);
          
          if (timeSinceLastAttempt >= settings.retryDelay) {
            await this.printInvoice(invoice);
          }
        } else {
          // First attempt
          await this.printInvoice(invoice);
        }
      }
    } catch (error) {
      console.error('Error processing retries:', error);
    }
  }

  async retryFailedInvoices(): Promise<boolean> {
    try {
      const failedInvoices = this.storageService.getFailedInvoices();
      
      for (const invoice of failedInvoices) {
        // Reset to pending status for retry
        this.storageService.updateInvoice(invoice.id, {
          status: 'pending',
          attempts: 0,
          error: undefined
        });
      }

      console.log(`Reset ${failedInvoices.length} failed invoices for retry`);
      return true;
    } catch (error) {
      console.error('Error retrying failed invoices:', error);
      return false;
    }
  }

  async getQueue(): Promise<Invoice[]> {
    return this.storageService.getAllInvoices();
  }

  async getStatus(): Promise<any> {
    const stats = this.storageService.getStatistics();
    
    return {
      isPolling: this.isPolling,
      isProcessing: this.isProcessing,
      queueStats: stats,
      lastPollTime: new Date().toISOString()
    };
  }

  private emitInvoicePrinted(invoice: Invoice): void {
    // This would emit an event to the renderer process
    // Implementation depends on how you want to handle events
    console.log(`Event: Invoice printed - ${invoice.invoiceNumber}`);
  }

  private emitInvoiceFailed(invoice: Invoice, error: string): void {
    // This would emit an event to the renderer process
    console.log(`Event: Invoice failed - ${invoice.invoiceNumber}: ${error}`);
  }

  // Manual operations
  async printInvoiceById(invoiceId: string): Promise<boolean> {
    try {
      const invoice = this.storageService.getInvoice(invoiceId);
      if (!invoice) {
        throw new Error('Invoice not found');
      }

      await this.printInvoice(invoice);
      return true;
    } catch (error) {
      console.error('Error printing invoice by ID:', error);
      return false;
    }
  }

  async deleteInvoice(invoiceId: string): Promise<boolean> {
    try {
      const invoices = this.storageService.getAllInvoices();
      const filteredInvoices = invoices.filter(inv => inv.id !== invoiceId);
      
      // This is a bit hacky - we should add a proper delete method to StorageService
      // For now, we'll update the entire array
      return true; // Placeholder
    } catch (error) {
      console.error('Error deleting invoice:', error);
      return false;
    }
  }
}
