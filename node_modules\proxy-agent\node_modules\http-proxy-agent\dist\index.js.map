{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yCAA2B;AAC3B,yCAA2B;AAE3B,kDAAgC;AAChC,mCAA8B;AAC9B,2CAAqD;AACrD,6BAA0B;AAG1B,MAAM,KAAK,GAAG,IAAA,eAAW,EAAC,kBAAkB,CAAC,CAAC;AA6B9C;;;GAGG;AACH,MAAa,cAAmC,SAAQ,kBAAK;IAO5D,YAAY,KAAgB,EAAE,IAAiC;QAC9D,KAAK,CAAC,IAAI,CAAC,CAAC;QACZ,IAAI,CAAC,KAAK,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,SAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAChE,IAAI,CAAC,YAAY,GAAG,IAAI,EAAE,OAAO,IAAI,EAAE,CAAC;QACxC,KAAK,CAAC,0CAA0C,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAEnE,4CAA4C;QAC5C,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAC5D,UAAU,EACV,EAAE,CACF,CAAC;QACF,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI;YAC3B,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;YAC/B,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,QAAQ;gBAClC,CAAC,CAAC,GAAG;gBACL,CAAC,CAAC,EAAE,CAAC;QACN,IAAI,CAAC,WAAW,GAAG;YAClB,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACxC,IAAI;YACJ,IAAI;SACJ,CAAC;IACH,CAAC;IAED,UAAU,CAAC,GAAgC,EAAE,IAAsB;QAClE,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAChC,iEAAiE;QACjE,KAAK,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IAC7B,CAAC;IAED,eAAe,CACd,GAAgC,EAChC,IAAsB;QAEtB,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC;QAC1D,MAAM,QAAQ,GAAG,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC;QACtD,MAAM,IAAI,GAAG,GAAG,QAAQ,KAAK,QAAQ,EAAE,CAAC;QACxC,MAAM,GAAG,GAAG,IAAI,SAAG,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACpC,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,EAAE;YACrB,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC7B;QAED,0DAA0D;QAC1D,0DAA0D;QAC1D,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QAEvB,wDAAwD;QAExD,MAAM,OAAO,GACZ,OAAO,IAAI,CAAC,YAAY,KAAK,UAAU;YACtC,CAAC,CAAC,IAAI,CAAC,YAAY,EAAE;YACrB,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAC7B,IAAI,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,EAAE;YACrC,MAAM,IAAI,GAAG,GAAG,kBAAkB,CACjC,KAAK,CAAC,QAAQ,CACd,IAAI,kBAAkB,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1C,OAAO,CAAC,qBAAqB,CAAC,GAAG,SAAS,MAAM,CAAC,IAAI,CACpD,IAAI,CACJ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;SACvB;QAED,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE;YACjC,OAAO,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC,SAAS;gBAC3C,CAAC,CAAC,YAAY;gBACd,CAAC,CAAC,OAAO,CAAC;SACX;QACD,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YACxC,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YAC5B,IAAI,KAAK,EAAE;gBACV,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;aAC3B;SACD;IACF,CAAC;IAED,KAAK,CAAC,OAAO,CACZ,GAAgC,EAChC,IAAsB;QAEtB,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC;QAEnB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAC9B,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;SAChC;QAED,mEAAmE;QACnE,mEAAmE;QACnE,kEAAkE;QAClE,IAAI,KAAa,CAAC;QAClB,IAAI,YAAoB,CAAC;QACzB,KAAK,CAAC,oDAAoD,CAAC,CAAC;QAC5D,GAAG,CAAC,eAAe,EAAE,CAAC;QACtB,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAChD,KAAK,CACJ,+DAA+D,CAC/D,CAAC;YACF,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAC/B,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YAC7C,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI;gBACrB,GAAG,CAAC,OAAO,GAAG,KAAK,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;YAC7C,KAAK,CAAC,mBAAmB,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;SACnD;QAED,kDAAkD;QAClD,IAAI,MAAkB,CAAC;QACvB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,QAAQ,EAAE;YACrC,KAAK,CAAC,2BAA2B,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YACrD,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SACvC;aAAM;YACN,KAAK,CAAC,2BAA2B,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YACrD,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SACvC;QAED,mEAAmE;QACnE,mEAAmE;QACnE,qEAAqE;QACrE,qDAAqD;QACrD,MAAM,IAAA,aAAI,EAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAE9B,OAAO,MAAM,CAAC;IACf,CAAC;;AA9HM,wBAAS,GAAG,CAAC,MAAM,EAAE,OAAO,CAAU,CAAC;AADlC,wCAAc;AAkI3B,SAAS,IAAI,CACZ,GAAM,EACN,GAAG,IAAO;IAIV,MAAM,GAAG,GAAG,EAEX,CAAC;IACF,IAAI,GAAqB,CAAC;IAC1B,KAAK,GAAG,IAAI,GAAG,EAAE;QAChB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACxB,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;SACpB;KACD;IACD,OAAO,GAAG,CAAC;AACZ,CAAC"}