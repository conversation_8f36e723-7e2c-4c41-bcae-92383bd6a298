{"name": "@electron-forge/template-vite-typescript", "version": "7.9.0", "description": "Vite-TypeScript template for Electron Forge, gets you started with Vite really quickly", "repository": {"type": "git", "url": "https://github.com/electron/forge", "directory": "packages/template/vite-typescript"}, "author": "caoxie<PERSON><PERSON>", "license": "MIT", "main": "dist/ViteTypeScriptTemplate.js", "typings": "dist/ViteTypeScriptTemplate.d.ts", "engines": {"node": ">= 16.4.0"}, "dependencies": {"@electron-forge/shared-types": "7.9.0", "@electron-forge/template-base": "7.9.0", "fs-extra": "^10.0.0"}, "devDependencies": {"@electron-forge/core-utils": "7.9.0", "@electron-forge/test-utils": "7.9.0", "fast-glob": "^3.2.7", "vitest": "^3.1.3"}, "files": ["dist", "src", "tmpl"], "gitHead": "cd63f57bd6870af2ad847076a183456221b30269"}