{"name": "ka<PERSON><PERSON>-crdt-printserver", "productName": "ka<PERSON><PERSON>-crdt-printserver", "version": "1.0.0", "description": "My Electron application description", "main": ".vite/build/main.js", "scripts": {"start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "eslint --ext .ts,.tsx ."}, "keywords": [], "author": "julia", "license": "MIT", "devDependencies": {"@electron-forge/cli": "^7.9.0", "@electron-forge/maker-deb": "^7.9.0", "@electron-forge/maker-rpm": "^7.9.0", "@electron-forge/maker-squirrel": "^7.9.0", "@electron-forge/maker-zip": "^7.9.0", "@electron-forge/plugin-auto-unpack-natives": "^7.9.0", "@electron-forge/plugin-fuses": "^7.9.0", "@electron-forge/plugin-vite": "^7.9.0", "@electron/fuses": "^1.8.0", "@types/electron-squirrel-startup": "^1.0.2", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "electron": "38.1.1", "eslint": "^8.57.1", "eslint-plugin-import": "^2.32.0", "typescript": "~4.5.4", "vite": "^5.4.20"}, "dependencies": {"@thesusheer/electron-printer": "^2.0.4", "@types/uuid": "^10.0.0", "axios": "^1.12.2", "bonjour-service": "^1.3.0", "cron": "^4.3.3", "electron-squirrel-startup": "^1.0.1", "electron-store": "^10.1.0", "network-scanner": "^0.0.2", "node-printer": "^1.0.4", "pdf-to-printer": "^5.6.0", "puppeteer-core": "^24.22.0", "uuid": "^13.0.0"}}