# Kassierer CRDT Print Server

A robust Electron-based print server application that automatically polls for invoices from a REST API and prints them without user intervention.

## Features

### Core Functionality

- **Automatic Invoice Polling**: Queries a REST API endpoint every minute for new invoices
- **Real Printer Discovery**: Automatically discovers available system and network printers using:
  - Windows PowerShell commands for local printers
  - Network printer discovery (mDNS/Bonjour support planned)
  - Support for multiple printer types (PDF, Inkjet, Laser, etc.)
- **Silent Printing**: Prints invoices immediately without showing system print dialogs
- **Queue Management**: Robust queue system with retry logic for failed prints
- **Duplicate Prevention**: Ensures no invoice is printed twice or lost
- **Offline Handling**: Saves invoices when printer is offline and retries when available
- **Real PDF Generation**: Uses Puppeteer with Chrome/Chromium for high-quality PDF output

### User Interface

- **Modern UI**: Clean, responsive interface with real-time status updates
- **Printer Selection**: Easy printer discovery and selection with persistent settings
- **Queue Monitoring**: View current print queue with status indicators
- **Statistics Dashboard**: Track printed, failed, and pending invoices
- **Error Handling**: Comprehensive error messages and recovery options

### Technical Features

- **Persistent Storage**: Local storage for settings, queue, and invoice tracking
- **IPC Communication**: Secure communication between main and renderer processes
- **Real Printer Integration**: Actual printer discovery and printing functionality
- **TypeScript**: Full TypeScript implementation for better code quality
- **Modern Architecture**: Service-based architecture with separation of concerns

## Installation

### Prerequisites

- Node.js 18+
- npm or yarn
- Windows, macOS, or Linux

### Setup

```bash
# Clone the repository
git clone <repository-url>
cd kassierer-crdt-printserver

# Install dependencies
npm install

# Start the development server
npm start

# Build for production
npm run make
```

## Usage

### Initial Setup

1. **Start the Application**: Run `npm start` to launch the print server
2. **Select a Printer**:
   - Click "🔄 Refresh Printers" to discover available printers
   - Select your desired printer from the dropdown
   - Click "🧪 Test Print" to verify the connection
3. **Start Polling**: Click "▶️ Start Polling" to begin monitoring for invoices

### API Configuration

The application polls a REST API endpoint for new invoices. Currently configured for:

- **Endpoint**: `http://localhost:3000/api/invoices` (configurable)
- **Polling Interval**: 60 seconds (configurable)
- **Expected Response Format**:

```json
{
  "invoices": [
    {
      "invoiceNumber": "INV-12345",
      "customerName": "Customer Name",
      "amount": 99.99,
      "items": [...],
      "date": "2024-01-01T00:00:00Z"
    }
  ],
  "hasMore": false
}
```

### Queue Management

- **View Queue**: Monitor all invoices in the queue with their current status
- **Retry Failed**: Manually retry all failed invoice prints
- **Status Indicators**:
  - 🟡 **Pending**: Waiting to be printed
  - 🔵 **Printing**: Currently being processed
  - 🟢 **Printed**: Successfully completed
  - 🔴 **Failed**: Print failed (will retry automatically)

## Architecture

### Services

- **StorageService**: Handles persistent data storage using electron-store
- **PrinterService**: Manages printer discovery and printing operations
- **InvoiceService**: Handles API polling, queue management, and retry logic

### Data Flow

1. **Polling**: InvoiceService polls the API every minute
2. **Processing**: New invoices are added to the queue and marked as pending
3. **Printing**: PrinterService attempts to print each invoice
4. **Tracking**: Status is updated and persisted to prevent duplicates
5. **Retry**: Failed prints are automatically retried with exponential backoff

### Storage Structure

```
AppData/
├── print-server-config.json    # App settings and printer configuration
└── invoice-queue.json          # Invoice queue and status tracking
```

## Configuration

### Settings (Configurable via StorageService)

```typescript
{
  apiEndpoint: "http://localhost:3000/api/invoices",
  pollingInterval: 60000,        // 1 minute in milliseconds
  maxRetryAttempts: 3,           // Maximum retry attempts for failed prints
  retryDelay: 5000              // Delay between retries in milliseconds
}
```

### Printer Settings

- Selected printer ID is persisted
- Printer-specific settings can be stored
- Automatic fallback to default printer if selected printer becomes unavailable

## Development

### Project Structure

```
src/
├── main.ts                 # Main Electron process
├── preload.ts             # Preload script for IPC
├── renderer.ts            # Renderer process (UI logic)
├── index.css             # Application styles
└── services/
    ├── StorageService.ts  # Data persistence
    ├── PrinterService.ts  # Printer management
    └── InvoiceService.ts  # API polling and queue management
```

### Real Implementation Status

The application now includes real printer functionality:

- **✅ Real Printer Discovery**: Uses Windows PowerShell commands to discover actual system printers
- **✅ Real PDF Generation**: Uses Puppeteer with Chrome/Chromium for actual PDF creation
- **✅ Real Printing**: Uses Windows printing commands to send documents to selected printers
- **🔄 Network Discovery**: mDNS/Bonjour network printer discovery (temporarily disabled, will be re-enabled)
- **✅ Mock API**: Generates random test invoices for testing (can be replaced with real API)

### Supported Printers

The system automatically detects and supports:

- **PDF Printers**: Microsoft Print to PDF, Adobe PDF
- **Network Printers**: Brother, HP, Canon, Kyocera, etc.
- **Local Printers**: Any Windows-installed printer
- **Virtual Printers**: OneNote, XPS Document Writer, Fax

### Building

```bash
# Development
npm start

# Build for current platform
npm run make

# Package for distribution
npm run package
```

## Error Handling

### Robust Error Recovery

- **Network Issues**: Graceful handling of API timeouts and connection failures
- **Printer Offline**: Automatic detection and retry when printer comes back online
- **Duplicate Prevention**: UUID-based tracking ensures no invoice is processed twice
- **Data Persistence**: All queue state is persisted to survive application restarts

### Logging

- Comprehensive console logging for debugging
- Error tracking with detailed error messages
- Status updates for monitoring application health

## Security

### IPC Security

- Context isolation enabled
- Node integration disabled in renderer
- Secure IPC communication with type-safe interfaces

### Data Protection

- Local storage only (no cloud dependencies)
- Encrypted storage for sensitive configuration
- No external network access except configured API endpoint

## Troubleshooting

### Common Issues

1. **No Printers Found**: Ensure printers are properly installed and accessible
2. **API Connection Failed**: Verify API endpoint is accessible and returns expected format
3. **Print Jobs Stuck**: Check printer status and restart if necessary
4. **High Memory Usage**: Clear old printed invoices from queue periodically

### Debug Mode

Enable debug logging by setting environment variable:

```bash
DEBUG=kassierer:* npm start
```

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## Support

For issues and feature requests, please use the GitHub issue tracker.
