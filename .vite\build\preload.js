"use strict";
const electron = require("electron");
electron.contextBridge.exposeInMainWorld("electronAPI", {
  // Printer operations
  getPrinters: () => electron.ipcRenderer.invoke("get-printers"),
  selectPrinter: (printerId) => electron.ipcRenderer.invoke("select-printer", printerId),
  getSelectedPrinter: () => electron.ipcRenderer.invoke("get-selected-printer"),
  // Invoice operations
  startInvoicePolling: () => electron.ipcRenderer.invoke("start-invoice-polling"),
  stopInvoicePolling: () => electron.ipcRenderer.invoke("stop-invoice-polling"),
  getInvoiceQueue: () => electron.ipcRenderer.invoke("get-invoice-queue"),
  retryFailedInvoices: () => electron.ipcRenderer.invoke("retry-failed-invoices"),
  // Status operations
  getPrintServerStatus: () => electron.ipcRenderer.invoke("get-print-server-status"),
  // Event listeners
  onInvoicePrinted: (callback) => {
    electron.ipcRenderer.on("invoice-printed", (_event, invoice) => callback(invoice));
  },
  onInvoiceFailed: (callback) => {
    electron.ipcRenderer.on(
      "invoice-failed",
      (_event, invoice, error) => callback(invoice, error)
    );
  },
  onPrinterStatusChanged: (callback) => {
    electron.ipcRenderer.on(
      "printer-status-changed",
      (_event, status) => callback(status)
    );
  },
  // Remove event listeners
  removeAllListeners: (channel) => {
    electron.ipcRenderer.removeAllListeners(channel);
  }
});
