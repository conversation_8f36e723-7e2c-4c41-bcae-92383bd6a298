{"name": "@electron-forge/cli", "version": "7.9.0", "description": "A complete tool for building modern Electron applications", "repository": "https://github.com/electron/forge", "author": "<PERSON>", "license": "MIT", "bin": {"electron-forge": "dist/electron-forge.js", "electron-forge-vscode-nix": "script/vscode.sh", "electron-forge-vscode-win": "script/vscode.cmd"}, "devDependencies": {"@malept/cross-spawn-promise": "^2.0.0", "vitest": "^3.1.3"}, "dependencies": {"@electron-forge/core": "7.9.0", "@electron-forge/core-utils": "7.9.0", "@electron-forge/shared-types": "7.9.0", "@electron/get": "^3.0.0", "@inquirer/prompts": "^6.0.1", "@listr2/prompt-adapter-inquirer": "^2.0.22", "chalk": "^4.0.0", "commander": "^11.1.0", "debug": "^4.3.1", "fs-extra": "^10.0.0", "listr2": "^7.0.2", "log-symbols": "^4.0.0", "semver": "^7.2.1"}, "engines": {"node": ">= 16.4.0"}, "funding": [{"type": "individual", "url": "https://github.com/sponsors/malept"}, {"type": "tidelift", "url": "https://tidelift.com/subscription/pkg/npm-.electron-forge-cli?utm_medium=referral&utm_source=npm_fund"}], "publishConfig": {"access": "public"}, "files": ["dist", "src", "script"], "gitHead": "cd63f57bd6870af2ad847076a183456221b30269"}