// See the Electron documentation for details on how to use preload scripts:
// https://www.electronjs.org/docs/latest/tutorial/process-model#preload-scripts

import { contextBridge, ipcRenderer } from "electron";

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld("electronAPI", {
  // Printer operations
  getPrinters: () => ipcRenderer.invoke("get-printers"),
  selectPrinter: (printerId: string) =>
    ipcRenderer.invoke("select-printer", printerId),
  getSelectedPrinter: () => ipcRenderer.invoke("get-selected-printer"),

  // Invoice operations
  startInvoicePolling: () => ipcRenderer.invoke("start-invoice-polling"),
  stopInvoicePolling: () => ipcRenderer.invoke("stop-invoice-polling"),
  getInvoiceQueue: () => ipcRenderer.invoke("get-invoice-queue"),
  retryFailedInvoices: () => ipcRenderer.invoke("retry-failed-invoices"),

  // Status operations
  getPrintServerStatus: () => ipcRenderer.invoke("get-print-server-status"),

  // Event listeners
  onInvoicePrinted: (callback: (invoice: any) => void) => {
    ipcRenderer.on("invoice-printed", (_event, invoice) => callback(invoice));
  },
  onInvoiceFailed: (callback: (invoice: any, error: string) => void) => {
    ipcRenderer.on("invoice-failed", (_event, invoice, error) =>
      callback(invoice, error)
    );
  },
  onPrinterStatusChanged: (callback: (status: any) => void) => {
    ipcRenderer.on("printer-status-changed", (_event, status) =>
      callback(status)
    );
  },

  // Remove event listeners
  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel);
  },
});

// Type definitions for the exposed API
declare global {
  interface Window {
    electronAPI: {
      getPrinters: () => Promise<any[]>;
      selectPrinter: (printerId: string) => Promise<boolean>;
      getSelectedPrinter: () => Promise<string | null>;
      startInvoicePolling: () => Promise<boolean>;
      stopInvoicePolling: () => Promise<boolean>;
      getInvoiceQueue: () => Promise<any[]>;
      retryFailedInvoices: () => Promise<boolean>;
      getPrintServerStatus: () => Promise<any>;
      onInvoicePrinted: (callback: (invoice: any) => void) => void;
      onInvoiceFailed: (
        callback: (invoice: any, error: string) => void
      ) => void;
      onPrinterStatusChanged: (callback: (status: any) => void) => void;
      removeAllListeners: (channel: string) => void;
    };
  }
}
