^C:\USERS\<USER>\PROJEKTE\KASSIERER-CRDT-PRINTSERVER\NODE_MODULES\@THESUSHEER\ELECTRON-PRINTER\BUILD\RELEASE\ELECTRON-PRINTER.NODE
call mkdir "C:\Users\<USER>\Projekte\kassierer-crdt-printserver\node_modules\@thesusheer\electron-printer\lib" 2>nul & set ERRORLEVEL=0 & copy /Y "C:\Users\<USER>\Projekte\kassierer-crdt-printserver\node_modules\@thesusheer\electron-printer\build\Release\electron-printer.node" "C:\Users\<USER>\Projekte\kassierer-crdt-printserver\node_modules\@thesusheer\electron-printer\lib\electron-printer.node"
if %errorlevel% neq 0 exit /b %errorlevel%
