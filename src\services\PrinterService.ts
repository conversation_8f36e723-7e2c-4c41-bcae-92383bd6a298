// const bonjour = require("bonjour-service");
import { exec } from "child_process";
import * as fs from "fs";
import * as os from "os";
import * as path from "path";
import puppeteer from "puppeteer-core";
import { promisify } from "util";
import { Invoice, StorageService } from "./StorageService";

const execAsync = promisify(exec);

export interface Printer {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  status?: string;
  isDefault?: boolean;
}

export interface PrintJob {
  invoiceId: string;
  filePath: string;
  printer: string;
  options?: any;
}

export class PrinterService {
  private storageService: StorageService;
  private tempDir: string;
  private bonjourInstance: any;

  constructor(storageService: StorageService) {
    this.storageService = storageService;
    this.tempDir = path.join(os.tmpdir(), "kassierer-print-server");

    // Ensure temp directory exists
    if (!fs.existsSync(this.tempDir)) {
      fs.mkdirSync(this.tempDir, { recursive: true });
    }

    // Initialize Bonjour for network printer discovery
    // this.bonjourInstance = bonjour();
    this.bonjourInstance = null; // Temporarily disabled
  }

  async getPrinters(): Promise<Printer[]> {
    try {
      const printers: Printer[] = [];

      // Get system printers (Windows)
      const systemPrinters = await this.getSystemPrinters();
      printers.push(...systemPrinters);

      // Get network printers via Bonjour/mDNS
      const networkPrinters = await this.getNetworkPrinters();
      printers.push(...networkPrinters);

      // Remove duplicates based on name
      const uniquePrinters = printers.filter(
        (printer, index, self) =>
          index === self.findIndex((p) => p.name === printer.name)
      );

      return uniquePrinters;
    } catch (error) {
      console.error("Error getting printers:", error);
      return [];
    }
  }

  async selectPrinter(printerId: string): Promise<boolean> {
    try {
      // Verify the printer exists
      const printers = await this.getPrinters();
      const printer = printers.find((p) => p.id === printerId);

      if (!printer) {
        throw new Error(`Printer with ID ${printerId} not found`);
      }

      this.storageService.setSelectedPrinter(printerId);
      return true;
    } catch (error) {
      console.error("Error selecting printer:", error);
      return false;
    }
  }

  async getSelectedPrinter(): Promise<string | null> {
    return this.storageService.getSelectedPrinter();
  }

  async printInvoice(invoice: Invoice): Promise<boolean> {
    const selectedPrinter = await this.getSelectedPrinter();

    if (!selectedPrinter) {
      throw new Error("No printer selected");
    }

    try {
      // Generate PDF from invoice data
      const pdfPath = await this.generateInvoicePDF(invoice);

      // Print the PDF
      await this.printPDF(pdfPath, selectedPrinter);

      // Clean up temp file
      this.cleanupTempFile(pdfPath);

      return true;
    } catch (error) {
      console.error("Error printing invoice:", error);
      throw error;
    }
  }

  private async generateInvoicePDF(invoice: Invoice): Promise<string> {
    // For now, create a simple HTML invoice and convert to PDF
    const html = this.generateInvoiceHTML(invoice);
    const pdfPath = path.join(this.tempDir, `invoice-${invoice.id}.pdf`);

    // Use puppeteer to generate PDF (we'll implement this)
    await this.htmlToPDF(html, pdfPath);

    return pdfPath;
  }

  private generateInvoiceHTML(invoice: Invoice): string {
    // Simple invoice template - this should be customizable
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Invoice ${invoice.invoiceNumber}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .invoice-number { font-size: 24px; font-weight: bold; }
          .details { margin: 20px 0; }
          .items { width: 100%; border-collapse: collapse; margin: 20px 0; }
          .items th, .items td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          .items th { background-color: #f2f2f2; }
          .total { text-align: right; font-weight: bold; font-size: 18px; }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="invoice-number">Invoice ${invoice.invoiceNumber}</div>
          <div>Date: ${new Date(invoice.createdAt).toLocaleDateString()}</div>
        </div>
        
        <div class="details">
          <h3>Invoice Details:</h3>
          <pre>${JSON.stringify(invoice.data, null, 2)}</pre>
        </div>
        
        <div class="footer">
          <p>Generated by Kassierer Print Server</p>
          <p>Printed at: ${new Date().toLocaleString()}</p>
        </div>
      </body>
      </html>
    `;
  }

  private async htmlToPDF(html: string, outputPath: string): Promise<void> {
    try {
      // Try to use Puppeteer for real PDF generation
      const browser = await puppeteer.launch({
        headless: true,
        executablePath: this.findChromePath(),
      });

      const page = await browser.newPage();
      await page.setContent(html, { waitUntil: "networkidle0" });

      await page.pdf({
        path: outputPath,
        format: "A4",
        printBackground: true,
        margin: {
          top: "1cm",
          right: "1cm",
          bottom: "1cm",
          left: "1cm",
        },
      });

      await browser.close();
    } catch (error) {
      console.warn(
        "Puppeteer PDF generation failed, falling back to text:",
        error
      );
      // Fallback to text file if Puppeteer fails
      const textContent = html
        .replace(/<[^>]*>/g, "")
        .replace(/\s+/g, " ")
        .trim();
      fs.writeFileSync(outputPath, textContent);
    }
  }

  private findChromePath(): string {
    // Try to find Chrome/Chromium executable
    const possiblePaths = [
      "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
      "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
      "C:\\Program Files\\Google\\Chrome Beta\\Application\\chrome.exe",
      "C:\\Program Files\\Chromium\\Application\\chrome.exe",
      "/usr/bin/google-chrome",
      "/usr/bin/chromium-browser",
      "/usr/bin/chromium",
      "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
    ];

    for (const chromePath of possiblePaths) {
      if (fs.existsSync(chromePath)) {
        return chromePath;
      }
    }

    throw new Error(
      "Chrome/Chromium not found. Please install Chrome or Chromium."
    );
  }

  private async printPDF(pdfPath: string, printerName: string): Promise<void> {
    try {
      console.log(`Printing: ${pdfPath} to printer: ${printerName}`);

      // Check if file exists
      if (!fs.existsSync(pdfPath)) {
        throw new Error(`File not found: ${pdfPath}`);
      }

      if (os.platform() === "win32") {
        // Use Windows printing command with specific printer
        // First try using Out-Printer which allows specifying the printer
        try {
          const command = `powershell "Get-Content -Path '${pdfPath}' -Raw | Out-Printer -Name '${printerName}'"`;
          await execAsync(command);
        } catch (error) {
          console.warn(
            "Out-Printer failed, trying alternative method:",
            error.message
          );

          // Fallback: Use default print verb (will use default printer)
          const fallbackCommand = `powershell "Start-Process -FilePath '${pdfPath}' -Verb Print -WindowStyle Hidden"`;
          await execAsync(fallbackCommand);
        }
      } else {
        // Use CUPS for Linux/macOS
        const command = `lp -d "${printerName}" "${pdfPath}"`;
        await execAsync(command);
      }

      console.log(`Successfully printed to ${printerName}`);
    } catch (error) {
      console.error("Printing failed:", error);
      throw new Error(`Failed to print to ${printerName}: ${error.message}`);
    }
  }

  private cleanupTempFile(filePath: string): void {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (error) {
      console.warn("Failed to cleanup temp file:", filePath, error);
    }
  }

  async getStatus(): Promise<any> {
    const selectedPrinter = await this.getSelectedPrinter();
    const printers = await this.getPrinters();

    return {
      selectedPrinter,
      availablePrinters: printers.length,
      printerOnline: selectedPrinter
        ? printers.some((p) => p.id === selectedPrinter)
        : false,
    };
  }

  // Test print functionality
  async testPrint(): Promise<boolean> {
    const selectedPrinter = await this.getSelectedPrinter();

    if (!selectedPrinter) {
      throw new Error("No printer selected");
    }

    try {
      // Create a simple test document
      const testHTML = `
        <html>
          <body>
            <h1>Print Server Test</h1>
            <p>This is a test print from Kassierer Print Server</p>
            <p>Date: ${new Date().toLocaleString()}</p>
            <p>Printer: ${selectedPrinter}</p>
          </body>
        </html>
      `;

      const testPdfPath = path.join(
        this.tempDir,
        `test-print-${Date.now()}.pdf`
      );
      await this.htmlToPDF(testHTML, testPdfPath);
      await this.printPDF(testPdfPath, selectedPrinter);
      this.cleanupTempFile(testPdfPath);

      return true;
    } catch (error) {
      console.error("Test print failed:", error);
      throw error;
    }
  }

  // Get system printers using Windows commands
  private async getSystemPrinters(): Promise<Printer[]> {
    try {
      const printers: Printer[] = [];

      if (os.platform() === "win32") {
        // Use PowerShell to get Windows printers
        const { stdout } = await execAsync(
          'powershell "Get-Printer | Select-Object Name,DriverName,PrinterStatus,Shared | ConvertTo-Json"'
        );

        const printerData = JSON.parse(stdout);
        const printerArray = Array.isArray(printerData)
          ? printerData
          : [printerData];

        for (const printer of printerArray) {
          if (printer.Name) {
            printers.push({
              id: printer.Name.replace(/\s+/g, "_"),
              name: printer.Name,
              displayName: printer.Name,
              description: printer.DriverName || "System printer",
              status: this.mapPrinterStatus(printer.PrinterStatus),
              isDefault: false, // We'll determine this separately
            });
          }
        }

        // Get default printer
        try {
          const { stdout: defaultStdout } = await execAsync(
            'powershell "(Get-WmiObject -Query \\"SELECT * FROM Win32_Printer WHERE Default=$true\\").Name"'
          );
          const defaultPrinterName = defaultStdout.trim();

          const defaultPrinter = printers.find(
            (p) => p.name === defaultPrinterName
          );
          if (defaultPrinter) {
            defaultPrinter.isDefault = true;
          }
        } catch (error) {
          console.warn("Could not determine default printer:", error);
        }
      } else {
        // For Linux/macOS, use CUPS
        try {
          const { stdout } = await execAsync("lpstat -p");
          const lines = stdout.split("\n");

          for (const line of lines) {
            if (line.startsWith("printer ")) {
              const match = line.match(/printer (\S+)/);
              if (match) {
                const printerName = match[1];
                printers.push({
                  id: printerName.replace(/\s+/g, "_"),
                  name: printerName,
                  displayName: printerName,
                  description: "CUPS printer",
                  status: line.includes("disabled") ? "Offline" : "Ready",
                  isDefault: false,
                });
              }
            }
          }
        } catch (error) {
          console.warn("CUPS not available or no printers found:", error);
        }
      }

      return printers;
    } catch (error) {
      console.error("Error getting system printers:", error);
      return [];
    }
  }

  // Get network printers using Bonjour/mDNS discovery
  private async getNetworkPrinters(): Promise<Printer[]> {
    // Temporarily disabled - will implement later
    console.log("Network printer discovery temporarily disabled");
    return [];
  }

  // Map Windows printer status to our status
  private mapPrinterStatus(status: number): string {
    // Windows printer status codes are bit flags
    if (status === 0) return "Ready";
    if (status & 0x00000001) return "Other";
    if (status & 0x00000002) return "Unknown";
    if (status & 0x00000004) return "Idle";
    if (status & 0x00000008) return "Printing";
    if (status & 0x00000010) return "Warmup";
    if (status & 0x00000020) return "Stopped";
    if (status & 0x00000040) return "Offline";
    if (status & 0x00000080) return "Paused";
    if (status & 0x00000100) return "Error";
    if (status & 0x00000200) return "Busy";
    if (status & 0x00000400) return "Not Available";
    if (status & 0x00000800) return "Waiting";
    if (status & 0x00001000) return "Processing";
    if (status & 0x00002000) return "Initializing";
    if (status & 0x00004000) return "Warming Up";
    if (status & 0x00008000) return "Toner Low";
    if (status & 0x00010000) return "No Toner";
    if (status & 0x00020000) return "Page Punt";
    if (status & 0x00040000) return "User Intervention Required";
    if (status & 0x00080000) return "Out of Memory";
    if (status & 0x00100000) return "Door Open";
    if (status & 0x00200000) return "Server Unknown";
    if (status & 0x00400000) return "Power Save";

    return `Status Code: ${status}`;
  }
}
