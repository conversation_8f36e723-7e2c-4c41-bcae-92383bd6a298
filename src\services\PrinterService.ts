import * as fs from "fs";
import * as os from "os";
import * as path from "path";
import { Invoice, StorageService } from "./StorageService";

export interface Printer {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  status?: string;
  isDefault?: boolean;
}

export interface PrintJob {
  invoiceId: string;
  filePath: string;
  printer: string;
  options?: any;
}

export class PrinterService {
  private storageService: StorageService;
  private tempDir: string;

  constructor(storageService: StorageService) {
    this.storageService = storageService;
    this.tempDir = path.join(os.tmpdir(), "kassierer-print-server");

    // Ensure temp directory exists
    if (!fs.existsSync(this.tempDir)) {
      fs.mkdirSync(this.tempDir, { recursive: true });
    }
  }

  async getPrinters(): Promise<Printer[]> {
    try {
      // For now, return mock printers for development
      // In production, this would use actual printer discovery
      const mockPrinters: Printer[] = [
        {
          id: "Microsoft_Print_to_PDF",
          name: "Microsoft Print to PDF",
          displayName: "Microsoft Print to PDF",
          description: "Virtual PDF printer",
          status: "Ready",
          isDefault: true,
        },
        {
          id: "HP_LaserJet_Pro",
          name: "HP LaserJet Pro",
          displayName: "HP LaserJet Pro M404dn",
          description: "Network printer",
          status: "Ready",
          isDefault: false,
        },
        {
          id: "Canon_PIXMA",
          name: "Canon PIXMA",
          displayName: "Canon PIXMA TS3300",
          description: "Inkjet printer",
          status: "Offline",
          isDefault: false,
        },
      ];

      // Simulate network delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      return mockPrinters;
    } catch (error) {
      console.error("Error getting printers:", error);
      return [];
    }
  }

  async selectPrinter(printerId: string): Promise<boolean> {
    try {
      // Verify the printer exists
      const printers = await this.getPrinters();
      const printer = printers.find((p) => p.id === printerId);

      if (!printer) {
        throw new Error(`Printer with ID ${printerId} not found`);
      }

      this.storageService.setSelectedPrinter(printerId);
      return true;
    } catch (error) {
      console.error("Error selecting printer:", error);
      return false;
    }
  }

  async getSelectedPrinter(): Promise<string | null> {
    return this.storageService.getSelectedPrinter();
  }

  async printInvoice(invoice: Invoice): Promise<boolean> {
    const selectedPrinter = await this.getSelectedPrinter();

    if (!selectedPrinter) {
      throw new Error("No printer selected");
    }

    try {
      // Generate PDF from invoice data
      const pdfPath = await this.generateInvoicePDF(invoice);

      // Print the PDF
      await this.printPDF(pdfPath, selectedPrinter);

      // Clean up temp file
      this.cleanupTempFile(pdfPath);

      return true;
    } catch (error) {
      console.error("Error printing invoice:", error);
      throw error;
    }
  }

  private async generateInvoicePDF(invoice: Invoice): Promise<string> {
    // For now, create a simple HTML invoice and convert to PDF
    const html = this.generateInvoiceHTML(invoice);
    const pdfPath = path.join(this.tempDir, `invoice-${invoice.id}.pdf`);

    // Use puppeteer to generate PDF (we'll implement this)
    await this.htmlToPDF(html, pdfPath);

    return pdfPath;
  }

  private generateInvoiceHTML(invoice: Invoice): string {
    // Simple invoice template - this should be customizable
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Invoice ${invoice.invoiceNumber}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .invoice-number { font-size: 24px; font-weight: bold; }
          .details { margin: 20px 0; }
          .items { width: 100%; border-collapse: collapse; margin: 20px 0; }
          .items th, .items td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          .items th { background-color: #f2f2f2; }
          .total { text-align: right; font-weight: bold; font-size: 18px; }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="invoice-number">Invoice ${invoice.invoiceNumber}</div>
          <div>Date: ${new Date(invoice.createdAt).toLocaleDateString()}</div>
        </div>
        
        <div class="details">
          <h3>Invoice Details:</h3>
          <pre>${JSON.stringify(invoice.data, null, 2)}</pre>
        </div>
        
        <div class="footer">
          <p>Generated by Kassierer Print Server</p>
          <p>Printed at: ${new Date().toLocaleString()}</p>
        </div>
      </body>
      </html>
    `;
  }

  private async htmlToPDF(html: string, outputPath: string): Promise<void> {
    // For now, write a simple text file as placeholder
    // In a real implementation, you'd use puppeteer or similar
    const textContent = html
      .replace(/<[^>]*>/g, "")
      .replace(/\s+/g, " ")
      .trim();
    fs.writeFileSync(outputPath, textContent);
  }

  private async printPDF(pdfPath: string, printerName: string): Promise<void> {
    try {
      // Mock printing implementation for development
      console.log(`Mock printing: ${pdfPath} to printer: ${printerName}`);

      // Simulate printing delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Check if file exists
      if (!fs.existsSync(pdfPath)) {
        throw new Error(`File not found: ${pdfPath}`);
      }

      // Simulate potential printer errors
      if (printerName.includes("Offline")) {
        throw new Error("Printer is offline");
      }

      console.log(`Successfully "printed" to ${printerName}`);
    } catch (error) {
      console.error("Mock printing failed:", error);
      throw error;
    }
  }

  private cleanupTempFile(filePath: string): void {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (error) {
      console.warn("Failed to cleanup temp file:", filePath, error);
    }
  }

  async getStatus(): Promise<any> {
    const selectedPrinter = await this.getSelectedPrinter();
    const printers = await this.getPrinters();

    return {
      selectedPrinter,
      availablePrinters: printers.length,
      printerOnline: selectedPrinter
        ? printers.some((p) => p.id === selectedPrinter)
        : false,
    };
  }

  // Test print functionality
  async testPrint(): Promise<boolean> {
    const selectedPrinter = await this.getSelectedPrinter();

    if (!selectedPrinter) {
      throw new Error("No printer selected");
    }

    try {
      // Create a simple test document
      const testHTML = `
        <html>
          <body>
            <h1>Print Server Test</h1>
            <p>This is a test print from Kassierer Print Server</p>
            <p>Date: ${new Date().toLocaleString()}</p>
            <p>Printer: ${selectedPrinter}</p>
          </body>
        </html>
      `;

      const testPdfPath = path.join(
        this.tempDir,
        `test-print-${Date.now()}.pdf`
      );
      await this.htmlToPDF(testHTML, testPdfPath);
      await this.printPDF(testPdfPath, selectedPrinter);
      this.cleanupTempFile(testPdfPath);

      return true;
    } catch (error) {
      console.error("Test print failed:", error);
      throw error;
    }
  }
}
