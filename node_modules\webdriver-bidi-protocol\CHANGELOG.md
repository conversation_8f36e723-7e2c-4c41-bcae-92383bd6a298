# Changelog

## [0.2.11](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.2.10...webdriver-bidi-protocol-v0.2.11) (2025-09-10)


### Bug Fixes

* add user agent override to the mapping ([#240](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/240)) ([0336e7f](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/0336e7faa0988ac4b0582a86345e5644ee8132bb))
* re-generate types based on specifciation updates ([#239](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/239)) ([2e52f5a](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/2e52f5ae40367410699d3a9820ad8856e7485712))
* re-generate types based on specifciation updates ([#242](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/242)) ([8ae63cc](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/8ae63cc2032ac3889b261d78c57b6b5cce412cad))

## [0.2.10](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.2.9...webdriver-bidi-protocol-v0.2.10) (2025-09-09)


### Bug Fixes

* re-generate types based on specifciation updates ([#236](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/236)) ([7b33604](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/7b336043c3aaf0591c9f4729c00b949563274b08))

## [0.2.9](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.2.8...webdriver-bidi-protocol-v0.2.9) (2025-09-09)


### Bug Fixes

* add emulation.setScriptingEnabled ([#234](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/234)) ([d1a7de7](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/d1a7de7dfb7abdc3178524457a5c011f32d08561))

## [0.2.8](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.2.7...webdriver-bidi-protocol-v0.2.8) (2025-09-09)


### Bug Fixes

* re-generate types based on specifciation updates ([#233](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/233)) ([42c83fd](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/42c83fd2e364a3097da2494b4d2f7e1a6419358b))
* use a helper for commands and events ([#231](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/231)) ([e302d10](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/e302d10cf32e02295b5910a541141d5435c80292))

## [0.2.7](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.2.6...webdriver-bidi-protocol-v0.2.7) (2025-09-08)


### Bug Fixes

* add emulation mapping ([#226](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/226)) ([f4851fe](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/f4851fe000293cad2aa38cf6d9290d4dd3dd27b4))
* re-generate types based on specifciation updates ([#229](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/229)) ([fbf9511](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/fbf951111ca4eaafea5aa03c02292aa9331a6b8e))

## [0.2.6](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.2.5...webdriver-bidi-protocol-v0.2.6) (2025-08-26)


### Bug Fixes

* re-generate types based on specifciation updates ([#221](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/221)) ([53d49a7](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/53d49a7ffcb5ef4de3f4f8f4828bf64e4552f599))

## [0.2.5](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.2.4...webdriver-bidi-protocol-v0.2.5) (2025-08-08)


### Bug Fixes

* re-generate types based on specifciation updates ([#213](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/213)) ([eb37bb4](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/eb37bb4fb84ec00e23fe9bfa58dbb250abd2f040))

## [0.2.4](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.2.3...webdriver-bidi-protocol-v0.2.4) (2025-08-01)


### Bug Fixes

* add latest commands to the mapping ([#207](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/207)) ([d53fd56](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/d53fd56538f068a88900475955a54a4ea74bf305))

## [0.2.3](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.2.2...webdriver-bidi-protocol-v0.2.3) (2025-08-01)


### Bug Fixes

* re-generate types based on specifciation updates ([#202](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/202)) ([db6f116](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/db6f116160068345715e17c686f9ddf3bf53423f))
* re-generate types based on specifciation updates ([#205](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/205)) ([8e75f89](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/8e75f892bdbce2360b271b4197154d9db65bb35a))

## [0.2.2](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.2.1...webdriver-bidi-protocol-v0.2.2) (2025-07-14)


### Bug Fixes

* re-generate types based on specifciation updates ([#190](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/190)) ([7387ba3](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/7387ba3287a187cc3a48d5ca4f735c650f8a2d1a))

## [0.2.1](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.2.0...webdriver-bidi-protocol-v0.2.1) (2025-07-01)


### Bug Fixes

* re-generate types based on specifciation updates ([#183](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/183)) ([daf944c](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/daf944c89cda0387cc0eac163c858515b60ea480))
* re-generate types based on specifciation updates ([#187](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/187)) ([d847f6a](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/d847f6afbd310e9587c79a942e33e09afe654729))

## [0.2.0](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.1.10...webdriver-bidi-protocol-v0.2.0) (2025-06-26)


### Features

* add bluetooth ([#66](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/66)) ([e42e8d9](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/e42e8d9896540020955d81b08066bdf92ed16602))


### Bug Fixes

* add repository ([#19](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/19)) ([91def9d](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/91def9dd0baeab3476e7154676e40d5a709da2c3))
* another repository fix ([72c215c](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/72c215cbcf76c97317deff925d1ff73732ed5bab))
* bump specs/webdriver-bidi from `0575bf7` to `9eaa877` ([#65](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/65)) ([d0ed8ef](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/d0ed8ef0254fb7786b5e2cd7b716aa772cc49fcd))
* bump specs/webdriver-bidi from `05a283e` to `62b8dfe` ([#16](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/16)) ([51dac71](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/51dac71ca4ea2e4e85eee7ba82a6a8ac7ff584a4))
* bump specs/webdriver-bidi from `0c366b0` to `a46d71b` ([#30](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/30)) ([c926381](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/c92638156ca8cbe36e2485fde2c3eca4b6d14cd7))
* bump specs/webdriver-bidi from `0fa2d99` to `712c17f` ([#36](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/36)) ([5e35acb](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/5e35acb0af5663fdace139da9648a298adf4b863))
* bump specs/webdriver-bidi from `287e9c6` to `0575bf7` ([#59](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/59)) ([20ec66a](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/20ec66ae46acdb49211991f603826bfbab0c0762))
* bump specs/webdriver-bidi from `62b8dfe` to `0c366b0` ([#25](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/25)) ([e6da243](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/e6da2439e38f2c403319ce1dbed16c9b8029667d))
* bump specs/webdriver-bidi from `712c17f` to `287e9c6` ([#43](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/43)) ([2f378f5](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/2f378f5890cbfec58e62f466b73fda0523083216))
* bump specs/webdriver-bidi from `a46d71b` to `0fa2d99` ([#33](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/33)) ([596a0ab](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/596a0ab776e061bccbc67a4fc38db4d5cce84643))
* document usage ([#7](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/7)) ([d98a6ee](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/d98a6ee23cbaa943440e2b40831a0c56aa0d6c18))
* re-gen types based on the spec changes ([#75](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/75)) ([e95b00e](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/e95b00e96333781b8843325d928e75f49768f06f))
* re-generate types based on specifciation updates ([#101](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/101)) ([c2aa431](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/c2aa431b0e7fe5c229f51d7b549fb6eb51009087))
* re-generate types based on specifciation updates ([#105](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/105)) ([adec09b](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/adec09b1c6fc6a29952a9c0a641ebd5d22f7370d))
* re-generate types based on specifciation updates ([#108](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/108)) ([00199dc](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/00199dc6e53e35cb12e1e67ce441a2d06f87e9ae))
* re-generate types based on specifciation updates ([#114](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/114)) ([f1a380a](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/f1a380afeebcb5f9a2ab66732f0971336c80bb0e))
* re-generate types based on specifciation updates ([#125](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/125)) ([95501b6](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/95501b6d1a81516aa55cd84af1a5d3f8c060174a))
* re-generate types based on specifciation updates ([#128](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/128)) ([852b2ab](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/852b2abfacb13c969f82f9507cccb50a2bbfeca0))
* re-generate types based on specifciation updates ([#140](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/140)) ([b769928](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/b769928b05cfc936a949384cc7b345f995895b54))
* re-generate types based on specifciation updates ([#143](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/143)) ([946a803](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/946a80305ad8c226519667a3c4d6bfc84d7a8fe1))
* re-generate types based on specifciation updates ([#147](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/147)) ([b568837](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/b5688371c4ce32b6d62ff6b1a08854f0c000bcbe))
* re-generate types based on specifciation updates ([#154](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/154)) ([5e9bfdd](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/5e9bfdde05a80e6852dbd81ac4e124702765b11c))
* re-generate types based on specifciation updates ([#156](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/156)) ([a917e79](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/a917e79b9abd17e312c52227310bfe8aa7e21abb))
* re-generate types based on specifciation updates ([#162](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/162)) ([bb92e98](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/bb92e98a232b060cc94a058fef92e10d8f8d6601))
* re-generate types based on specifciation updates ([#166](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/166)) ([42664dd](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/42664dd5c7401588e55dda5573c3b7096f700953))
* re-generate types based on specifciation updates ([#178](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/178)) ([6ee5f05](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/6ee5f05ae9298e59ff34c4859543bebcc4ad658d))
* re-generate types based on specifciation updates ([#83](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/83)) ([ac66a29](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/ac66a29bc3396c2bdabb133d5dfb63fc2b029add))
* re-generate types based on specifciation updates ([#88](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/88)) ([d3c2c6f](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/d3c2c6fcf973551d4bef5cbee5291e3581ae257c))
* re-generate types based on specifciation updates ([#95](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/95)) ([64abd1d](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/64abd1d65d99e75d7ec1e885fe22c0f093323843))
* update repository ([28cf911](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/28cf911e59ad5b177df47d04fc012d8c9cfe7fde))

## [0.1.10](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.1.9...webdriver-bidi-protocol-v0.1.10) (2025-06-26)


### Bug Fixes

* re-generate types based on specifciation updates ([#178](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/178)) ([6ee5f05](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/6ee5f05ae9298e59ff34c4859543bebcc4ad658d))

## [0.1.9](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.1.8...webdriver-bidi-protocol-v0.1.9) (2025-06-03)


### Bug Fixes

* re-generate types based on specifciation updates ([#147](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/147)) ([b568837](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/b5688371c4ce32b6d62ff6b1a08854f0c000bcbe))
* re-generate types based on specifciation updates ([#154](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/154)) ([5e9bfdd](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/5e9bfdde05a80e6852dbd81ac4e124702765b11c))
* re-generate types based on specifciation updates ([#156](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/156)) ([a917e79](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/a917e79b9abd17e312c52227310bfe8aa7e21abb))
* re-generate types based on specifciation updates ([#162](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/162)) ([bb92e98](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/bb92e98a232b060cc94a058fef92e10d8f8d6601))
* re-generate types based on specifciation updates ([#166](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/166)) ([42664dd](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/42664dd5c7401588e55dda5573c3b7096f700953))

## [0.1.8](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.1.7...webdriver-bidi-protocol-v0.1.8) (2025-04-04)


### Bug Fixes

* re-generate types based on specifciation updates ([#140](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/140)) ([b769928](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/b769928b05cfc936a949384cc7b345f995895b54))
* re-generate types based on specifciation updates ([#143](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/143)) ([946a803](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/946a80305ad8c226519667a3c4d6bfc84d7a8fe1))

## [0.1.7](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.1.6...webdriver-bidi-protocol-v0.1.7) (2025-03-06)


### Bug Fixes

* re-generate types based on specifciation updates ([#114](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/114)) ([f1a380a](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/f1a380afeebcb5f9a2ab66732f0971336c80bb0e))
* re-generate types based on specifciation updates ([#125](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/125)) ([95501b6](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/95501b6d1a81516aa55cd84af1a5d3f8c060174a))
* re-generate types based on specifciation updates ([#128](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/128)) ([852b2ab](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/852b2abfacb13c969f82f9507cccb50a2bbfeca0))

## [0.1.6](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.1.5...webdriver-bidi-protocol-v0.1.6) (2025-01-20)


### Bug Fixes

* re-generate types based on specifciation updates ([#108](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/108)) ([00199dc](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/00199dc6e53e35cb12e1e67ce441a2d06f87e9ae))

## [0.1.5](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.1.4...webdriver-bidi-protocol-v0.1.5) (2025-01-19)


### Bug Fixes

* re-generate types based on specifciation updates ([#101](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/101)) ([c2aa431](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/c2aa431b0e7fe5c229f51d7b549fb6eb51009087))
* re-generate types based on specifciation updates ([#105](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/105)) ([adec09b](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/adec09b1c6fc6a29952a9c0a641ebd5d22f7370d))

## [0.1.4](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.1.3...webdriver-bidi-protocol-v0.1.4) (2025-01-08)


### Bug Fixes

* re-generate types based on specifciation updates ([#95](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/95)) ([64abd1d](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/64abd1d65d99e75d7ec1e885fe22c0f093323843))

## [0.1.3](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.1.2...webdriver-bidi-protocol-v0.1.3) (2024-12-20)


### Bug Fixes

* re-generate types based on specifciation updates ([#88](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/88)) ([d3c2c6f](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/d3c2c6fcf973551d4bef5cbee5291e3581ae257c))

## [0.1.2](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.1.1...webdriver-bidi-protocol-v0.1.2) (2024-12-08)


### Bug Fixes

* re-generate types based on specifciation updates ([#83](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/83)) ([ac66a29](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/ac66a29bc3396c2bdabb133d5dfb63fc2b029add))

## [0.1.1](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.1.0...webdriver-bidi-protocol-v0.1.1) (2024-12-03)


### Bug Fixes

* re-gen types based on the spec changes ([#75](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/75)) ([e95b00e](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/e95b00e96333781b8843325d928e75f49768f06f))

## [0.1.0](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.0.9...webdriver-bidi-protocol-v0.1.0) (2024-12-02)


### Features

* add bluetooth ([#66](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/66)) ([e42e8d9](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/e42e8d9896540020955d81b08066bdf92ed16602))


### Bug Fixes

* add repository ([#19](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/19)) ([91def9d](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/91def9dd0baeab3476e7154676e40d5a709da2c3))
* another repository fix ([72c215c](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/72c215cbcf76c97317deff925d1ff73732ed5bab))
* bump specs/webdriver-bidi from `0575bf7` to `9eaa877` ([#65](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/65)) ([d0ed8ef](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/d0ed8ef0254fb7786b5e2cd7b716aa772cc49fcd))
* bump specs/webdriver-bidi from `05a283e` to `62b8dfe` ([#16](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/16)) ([51dac71](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/51dac71ca4ea2e4e85eee7ba82a6a8ac7ff584a4))
* bump specs/webdriver-bidi from `0c366b0` to `a46d71b` ([#30](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/30)) ([c926381](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/c92638156ca8cbe36e2485fde2c3eca4b6d14cd7))
* bump specs/webdriver-bidi from `0fa2d99` to `712c17f` ([#36](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/36)) ([5e35acb](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/5e35acb0af5663fdace139da9648a298adf4b863))
* bump specs/webdriver-bidi from `287e9c6` to `0575bf7` ([#59](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/59)) ([20ec66a](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/20ec66ae46acdb49211991f603826bfbab0c0762))
* bump specs/webdriver-bidi from `62b8dfe` to `0c366b0` ([#25](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/25)) ([e6da243](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/e6da2439e38f2c403319ce1dbed16c9b8029667d))
* bump specs/webdriver-bidi from `712c17f` to `287e9c6` ([#43](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/43)) ([2f378f5](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/2f378f5890cbfec58e62f466b73fda0523083216))
* bump specs/webdriver-bidi from `a46d71b` to `0fa2d99` ([#33](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/33)) ([596a0ab](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/596a0ab776e061bccbc67a4fc38db4d5cce84643))
* document usage ([#7](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/7)) ([d98a6ee](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/d98a6ee23cbaa943440e2b40831a0c56aa0d6c18))
* update repository ([28cf911](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/28cf911e59ad5b177df47d04fc012d8c9cfe7fde))

## [0.0.9](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.0.8...webdriver-bidi-protocol-v0.0.9) (2024-09-03)


### Bug Fixes

* bump specs/webdriver-bidi from `0fa2d99` to `712c17f` ([#36](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/36)) ([5e35acb](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/5e35acb0af5663fdace139da9648a298adf4b863))
* bump specs/webdriver-bidi from `712c17f` to `287e9c6` ([#43](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/43)) ([2f378f5](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/2f378f5890cbfec58e62f466b73fda0523083216))

## [0.0.8](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.0.7...webdriver-bidi-protocol-v0.0.8) (2024-07-10)


### Bug Fixes

* bump specs/webdriver-bidi from `a46d71b` to `0fa2d99` ([#33](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/33)) ([596a0ab](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/596a0ab776e061bccbc67a4fc38db4d5cce84643))

## [0.0.7](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.0.6...webdriver-bidi-protocol-v0.0.7) (2024-07-04)


### Bug Fixes

* bump specs/webdriver-bidi from `0c366b0` to `a46d71b` ([#30](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/30)) ([c926381](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/c92638156ca8cbe36e2485fde2c3eca4b6d14cd7))

## [0.0.6](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.0.5...webdriver-bidi-protocol-v0.0.6) (2024-07-02)


### Bug Fixes

* bump specs/webdriver-bidi from `62b8dfe` to `0c366b0` ([#25](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/25)) ([e6da243](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/e6da2439e38f2c403319ce1dbed16c9b8029667d))

## [0.0.5](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.0.4...webdriver-bidi-protocol-v0.0.5) (2024-06-28)


### Bug Fixes

* another repository fix ([72c215c](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/72c215cbcf76c97317deff925d1ff73732ed5bab))

## [0.0.4](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.0.3...webdriver-bidi-protocol-v0.0.4) (2024-06-28)


### Bug Fixes

* update repository ([28cf911](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/28cf911e59ad5b177df47d04fc012d8c9cfe7fde))

## [0.0.3](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.0.2...webdriver-bidi-protocol-v0.0.3) (2024-06-28)


### Bug Fixes

* add repository ([#19](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/19)) ([91def9d](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/91def9dd0baeab3476e7154676e40d5a709da2c3))

## [0.0.2](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/compare/webdriver-bidi-protocol-v0.0.1...webdriver-bidi-protocol-v0.0.2) (2024-06-28)


### Bug Fixes

* bump specs/webdriver-bidi from `05a283e` to `62b8dfe` ([#16](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/16)) ([51dac71](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/51dac71ca4ea2e4e85eee7ba82a6a8ac7ff584a4))
* document usage ([#7](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/issues/7)) ([d98a6ee](https://github.com/GoogleChromeLabs/webdriver-bidi-protocol/commit/d98a6ee23cbaa943440e2b40831a0c56aa0d6c18))
