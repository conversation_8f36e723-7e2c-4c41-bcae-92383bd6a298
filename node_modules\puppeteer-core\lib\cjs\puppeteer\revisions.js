"use strict";
/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PUPPETEER_REVISIONS = void 0;
/**
 * @internal
 */
exports.PUPPETEER_REVISIONS = Object.freeze({
    chrome: '140.0.7339.82',
    'chrome-headless-shell': '140.0.7339.82',
    firefox: 'stable_143.0',
});
//# sourceMappingURL=revisions.js.map