{"version": 3, "file": "BrowserLauncher.d.ts", "sourceRoot": "", "sources": ["../../../../src/node/BrowserLauncher.ts"], "names": [], "mappings": "AASA,OAAO,EAGL,MAAM,EAIP,MAAM,qBAAqB,CAAC;AAS7B,OAAO,KAAK,EAAC,OAAO,EAAE,oBAAoB,EAAC,MAAM,mBAAmB,CAAC;AAErE,OAAO,EAAC,UAAU,EAAC,MAAM,sBAAsB,CAAC;AAEhD,OAAO,KAAK,EAAC,gBAAgB,EAAC,MAAM,+BAA+B,CAAC;AAEpE,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,uBAAuB,CAAC;AAEpD,OAAO,KAAK,EAAC,oBAAoB,EAAE,aAAa,EAAC,MAAM,oBAAoB,CAAC;AAG5E,OAAO,KAAK,EAAC,aAAa,EAAC,MAAM,oBAAoB,CAAC;AAEtD;;GAEG;AACH,MAAM,WAAW,kBAAkB;IACjC,iBAAiB,EAAE,OAAO,CAAC;IAC3B,WAAW,EAAE,MAAM,CAAC;IACpB,cAAc,EAAE,MAAM,CAAC;IACvB,IAAI,EAAE,MAAM,EAAE,CAAC;CAChB;AAED;;;;GAIG;AACH,8BAAsB,eAAe;;IAGnC;;OAEG;IACH,SAAS,EAAE,aAAa,CAAC;IAEzB;;OAEG;gBACS,SAAS,EAAE,aAAa,EAAE,OAAO,EAAE,gBAAgB;IAK/D,IAAI,OAAO,IAAI,gBAAgB,CAE9B;IAEK,MAAM,CAAC,OAAO,GAAE,aAAkB,GAAG,OAAO,CAAC,OAAO,CAAC;IAkL3D,QAAQ,CAAC,cAAc,CACrB,OAAO,CAAC,EAAE,oBAAoB,EAC9B,YAAY,CAAC,EAAE,OAAO,GACrB,MAAM;IAET,QAAQ,CAAC,WAAW,CAAC,MAAM,EAAE,aAAa,GAAG,MAAM,EAAE;IAErD;;OAEG;IACH,SAAS,CAAC,QAAQ,CAAC,sBAAsB,CACvC,OAAO,EAAE,aAAa,GACrB,OAAO,CAAC,kBAAkB,CAAC;IAE9B;;OAEG;IACH,SAAS,CAAC,QAAQ,CAAC,gBAAgB,CACjC,IAAI,EAAE,MAAM,EACZ,IAAI,EAAE;QAAC,MAAM,EAAE,OAAO,CAAA;KAAC,GACtB,OAAO,CAAC,IAAI,CAAC;IAEhB;;OAEG;cACa,YAAY,CAC1B,cAAc,EAAE,UAAU,CAAC,OAAO,MAAM,CAAC,EACzC,aAAa,CAAC,EAAE,UAAU,GACzB,OAAO,CAAC,IAAI,CAAC;IAyBhB;;OAEG;cACa,iBAAiB,CAC/B,OAAO,EAAE,OAAO,EAChB,OAAO,EAAE,MAAM,GACd,OAAO,CAAC,IAAI,CAAC;IAchB;;OAEG;cACa,yBAAyB,CACvC,cAAc,EAAE,UAAU,CAAC,OAAO,MAAM,CAAC,EACzC,IAAI,EAAE;QACJ,OAAO,EAAE,MAAM,CAAC;QAChB,eAAe,EAAE,MAAM,GAAG,SAAS,CAAC;QACpC,MAAM,EAAE,MAAM,CAAC;KAChB,GACA,OAAO,CAAC,UAAU,CAAC;IActB;;OAEG;cACa,uBAAuB,CACrC,cAAc,EAAE,UAAU,CAAC,OAAO,MAAM,CAAC,EACzC,IAAI,EAAE;QACJ,OAAO,EAAE,MAAM,CAAC;QAChB,eAAe,EAAE,MAAM,GAAG,SAAS,CAAC;QACpC,MAAM,EAAE,MAAM,CAAC;KAChB,GACA,OAAO,CAAC,UAAU,CAAC;IAWtB;;OAEG;cACa,wBAAwB,CACtC,cAAc,EAAE,UAAU,CAAC,OAAO,MAAM,CAAC,EACzC,UAAU,EAAE,UAAU,EACtB,aAAa,EAAE,oBAAoB,EACnC,IAAI,EAAE;QACJ,eAAe,EAAE,QAAQ,GAAG,IAAI,CAAC;QACjC,mBAAmB,CAAC,EAAE,OAAO,CAAC;QAC9B,cAAc,EAAE,OAAO,CAAC;KACzB,GACA,OAAO,CAAC,OAAO,CAAC;IAcnB;;OAEG;cACa,iBAAiB,CAC/B,cAAc,EAAE,UAAU,CAAC,OAAO,MAAM,CAAC,EACzC,aAAa,EAAE,oBAAoB,EACnC,IAAI,EAAE;QACJ,OAAO,EAAE,MAAM,CAAC;QAChB,eAAe,EAAE,MAAM,GAAG,SAAS,CAAC;QACpC,MAAM,EAAE,MAAM,CAAC;QACf,eAAe,EAAE,QAAQ,GAAG,IAAI,CAAC;QACjC,mBAAmB,CAAC,EAAE,OAAO,CAAC;QAC9B,cAAc,CAAC,EAAE,OAAO,CAAC;KAC1B,GACA,OAAO,CAAC,OAAO,CAAC;IAwBnB;;OAEG;IACH,SAAS,CAAC,cAAc,IAAI,MAAM;IAOlC;;OAEG;IACH,qBAAqB,CACnB,QAAQ,CAAC,EAAE,OAAO,GAAG,OAAO,EAC5B,YAAY,UAAO,GAClB,MAAM;CAiEV"}