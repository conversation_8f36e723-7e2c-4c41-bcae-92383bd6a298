{"name": "map-age-cleaner", "version": "0.1.3", "description": "Automatically cleanup expired items in a Map", "license": "MIT", "repository": "SamVerschueren/map-age-cleaner", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/SamVerschueren"}, "main": "dist/index.js", "engines": {"node": ">=6"}, "scripts": {"prepublishOnly": "npm run build", "pretest": "npm run build -- --sourceMap", "test": "npm run lint && nyc ava dist/test.js", "lint": "tslint --format stylish --project .", "build": "npm run clean && tsc", "clean": "del-cli dist"}, "files": ["dist/index.js", "dist/index.d.ts"], "keywords": ["map", "age", "cleaner", "maxage", "expire", "expiration", "expiring"], "dependencies": {"p-defer": "^1.0.0"}, "devDependencies": {"@types/delay": "^2.0.1", "@types/node": "^10.7.1", "ava": "^0.25.0", "codecov": "^3.0.0", "del-cli": "^1.1.0", "delay": "^3.0.0", "nyc": "^12.0.0", "tslint": "^5.11.0", "tslint-xo": "^0.9.0", "typescript": "^3.0.1"}, "typings": "dist/index.d.ts", "sideEffects": false, "nyc": {"exclude": ["dist/test.js"]}}