{"name": "p-defer", "version": "1.0.0", "description": "Create a deferred promise", "license": "MIT", "repository": "sindresorhus/p-defer", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "defer", "deferred", "resolve", "reject", "lazy", "later", "async", "await", "promises", "bluebird"], "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}}