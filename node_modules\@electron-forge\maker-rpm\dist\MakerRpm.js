"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MakerRpm = exports.rpmArch = void 0;
const node_path_1 = __importDefault(require("node:path"));
const maker_base_1 = require("@electron-forge/maker-base");
function renameRpm(dest, _src) {
    return node_path_1.default.join(dest, '<%= name %>-<%= version %>-<%= revision %>.<%= arch === "aarch64" ? "arm64" : arch %>.rpm');
}
function rpmArch(nodeArch) {
    switch (nodeArch) {
        case 'ia32':
            return 'i386';
        case 'x64':
            return 'x86_64';
        case 'arm64':
            return 'aarch64';
        case 'armv7l':
            return 'armv7hl';
        case 'arm':
            return 'armv6hl';
        default:
            return nodeArch;
    }
}
exports.rpmArch = rpmArch;
class MakerRpm extends maker_base_1.MakerBase {
    constructor() {
        super(...arguments);
        this.name = 'rpm';
        this.defaultPlatforms = ['linux'];
        this.requiredExternalBinaries = ['rpmbuild'];
    }
    isSupportedOnCurrentPlatform() {
        return this.isInstalled('electron-installer-redhat');
    }
    async make({ dir, makeDir, targetArch }) {
        // eslint-disable-next-line n/no-missing-require
        const installer = require('electron-installer-redhat');
        const outDir = node_path_1.default.resolve(makeDir, 'rpm', targetArch);
        await this.ensureDirectory(outDir);
        const { packagePaths } = await installer({
            ...this.config,
            arch: rpmArch(targetArch),
            src: dir,
            dest: outDir,
            rename: renameRpm,
        });
        return packagePaths;
    }
}
exports.default = MakerRpm;
exports.MakerRpm = MakerRpm;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiTWFrZXJScG0uanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi9zcmMvTWFrZXJScG0udHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsMERBQTZCO0FBRTdCLDJEQUFxRTtBQUtyRSxTQUFTLFNBQVMsQ0FBQyxJQUFZLEVBQUUsSUFBWTtJQUMzQyxPQUFPLG1CQUFJLENBQUMsSUFBSSxDQUNkLElBQUksRUFDSiwyRkFBMkYsQ0FDNUYsQ0FBQztBQUNKLENBQUM7QUFFRCxTQUFnQixPQUFPLENBQUMsUUFBbUI7SUFDekMsUUFBUSxRQUFRLEVBQUUsQ0FBQztRQUNqQixLQUFLLE1BQU07WUFDVCxPQUFPLE1BQU0sQ0FBQztRQUNoQixLQUFLLEtBQUs7WUFDUixPQUFPLFFBQVEsQ0FBQztRQUNsQixLQUFLLE9BQU87WUFDVixPQUFPLFNBQVMsQ0FBQztRQUNuQixLQUFLLFFBQVE7WUFDWCxPQUFPLFNBQVMsQ0FBQztRQUNuQixLQUFLLEtBQUs7WUFDUixPQUFPLFNBQVMsQ0FBQztRQUNuQjtZQUNFLE9BQU8sUUFBUSxDQUFDO0lBQ3BCLENBQUM7QUFDSCxDQUFDO0FBZkQsMEJBZUM7QUFFRCxNQUFxQixRQUFTLFNBQVEsc0JBQXlCO0lBQS9EOztRQUNFLFNBQUksR0FBRyxLQUFLLENBQUM7UUFFYixxQkFBZ0IsR0FBb0IsQ0FBQyxPQUFPLENBQUMsQ0FBQztRQUU5Qyw2QkFBd0IsR0FBYSxDQUFDLFVBQVUsQ0FBQyxDQUFDO0lBc0JwRCxDQUFDO0lBcEJDLDRCQUE0QjtRQUMxQixPQUFPLElBQUksQ0FBQyxXQUFXLENBQUMsMkJBQTJCLENBQUMsQ0FBQztJQUN2RCxDQUFDO0lBRUQsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLEdBQUcsRUFBRSxPQUFPLEVBQUUsVUFBVSxFQUFnQjtRQUNuRCxnREFBZ0Q7UUFDaEQsTUFBTSxTQUFTLEdBQUcsT0FBTyxDQUFDLDJCQUEyQixDQUFDLENBQUM7UUFFdkQsTUFBTSxNQUFNLEdBQUcsbUJBQUksQ0FBQyxPQUFPLENBQUMsT0FBTyxFQUFFLEtBQUssRUFBRSxVQUFVLENBQUMsQ0FBQztRQUV4RCxNQUFNLElBQUksQ0FBQyxlQUFlLENBQUMsTUFBTSxDQUFDLENBQUM7UUFDbkMsTUFBTSxFQUFFLFlBQVksRUFBRSxHQUFHLE1BQU0sU0FBUyxDQUFDO1lBQ3ZDLEdBQUcsSUFBSSxDQUFDLE1BQU07WUFDZCxJQUFJLEVBQUUsT0FBTyxDQUFDLFVBQVUsQ0FBQztZQUN6QixHQUFHLEVBQUUsR0FBRztZQUNSLElBQUksRUFBRSxNQUFNO1lBQ1osTUFBTSxFQUFFLFNBQVM7U0FDbEIsQ0FBQyxDQUFDO1FBQ0gsT0FBTyxZQUFZLENBQUM7SUFDdEIsQ0FBQztDQUNGO0FBM0JELDJCQTJCQztBQUVRLDRCQUFRIn0=