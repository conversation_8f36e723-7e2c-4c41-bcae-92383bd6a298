{"name": "@electron-forge/publisher-base", "version": "7.9.0", "description": "Base publisher for Electron Forge", "repository": "https://github.com/electron/forge", "author": "<PERSON>", "license": "MIT", "main": "dist/Publisher.js", "typings": "dist/Publisher.d.ts", "dependencies": {"@electron-forge/shared-types": "7.9.0"}, "devDependencies": {"vitest": "^3.1.3"}, "engines": {"node": ">= 16.4.0"}, "publishConfig": {"access": "public"}, "files": ["dist", "src"], "gitHead": "cd63f57bd6870af2ad847076a183456221b30269"}