"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sudo = void 0;
const node_child_process_1 = require("node:child_process");
const node_util_1 = require("node:util");
const sudo_prompt_1 = __importDefault(require("@vscode/sudo-prompt"));
const which = async (type, prog, 
// eslint-disable-next-line @typescript-eslint/no-explicit-any
promise) => {
    if ((0, node_child_process_1.spawnSync)('which', [prog]).status === 0) {
        await promise();
    }
    else {
        throw new Error(`${prog} is required to install ${type} packages`);
    }
};
const sudo = (type, prog, args) => which(type, prog, () => (0, node_util_1.promisify)(sudo_prompt_1.default.exec)(`${prog} ${args}`, { name: 'Electron Forge' }));
exports.sudo = sudo;
exports.default = which;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibGludXgtaW5zdGFsbGVyLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vc3JjL3V0aWwvbGludXgtaW5zdGFsbGVyLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLDJEQUErQztBQUMvQyx5Q0FBc0M7QUFFdEMsc0VBQTZDO0FBRTdDLE1BQU0sS0FBSyxHQUFHLEtBQUssRUFDakIsSUFBWSxFQUNaLElBQVk7QUFDWiw4REFBOEQ7QUFDOUQsT0FBMkIsRUFDWixFQUFFO0lBQ2pCLElBQUksSUFBQSw4QkFBUyxFQUFDLE9BQU8sRUFBRSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsTUFBTSxLQUFLLENBQUMsRUFBRSxDQUFDO1FBQzVDLE1BQU0sT0FBTyxFQUFFLENBQUM7SUFDbEIsQ0FBQztTQUFNLENBQUM7UUFDTixNQUFNLElBQUksS0FBSyxDQUFDLEdBQUcsSUFBSSwyQkFBMkIsSUFBSSxXQUFXLENBQUMsQ0FBQztJQUNyRSxDQUFDO0FBQ0gsQ0FBQyxDQUFDO0FBRUssTUFBTSxJQUFJLEdBQUcsQ0FBQyxJQUFZLEVBQUUsSUFBWSxFQUFFLElBQVksRUFBaUIsRUFBRSxDQUM5RSxLQUFLLENBQUMsSUFBSSxFQUFFLElBQUksRUFBRSxHQUFHLEVBQUUsQ0FDckIsSUFBQSxxQkFBUyxFQUFDLHFCQUFVLENBQUMsSUFBSSxDQUFDLENBQUMsR0FBRyxJQUFJLElBQUksSUFBSSxFQUFFLEVBQUUsRUFBRSxJQUFJLEVBQUUsZ0JBQWdCLEVBQUUsQ0FBQyxDQUMxRSxDQUFDO0FBSFMsUUFBQSxJQUFJLFFBR2I7QUFFSixrQkFBZSxLQUFLLENBQUMifQ==