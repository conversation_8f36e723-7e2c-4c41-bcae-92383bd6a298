{"name": "@electron-forge/plugin-auto-unpack-natives", "version": "7.9.0", "description": "Auto Unpack Natives plugin for Electron Forge, automatically adds native node modules to asar.unpacked", "repository": "https://github.com/electron/forge", "author": "<PERSON>", "license": "MIT", "main": "dist/AutoUnpackNativesPlugin.js", "typings": "dist/AutoUnpackNativesPlugin.d.ts", "engines": {"node": ">= 16.4.0"}, "dependencies": {"@electron-forge/plugin-base": "7.9.0", "@electron-forge/shared-types": "7.9.0"}, "publishConfig": {"access": "public"}, "files": ["dist", "src"], "gitHead": "cd63f57bd6870af2ad847076a183456221b30269"}