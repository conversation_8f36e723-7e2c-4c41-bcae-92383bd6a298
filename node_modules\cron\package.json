{"name": "cron", "description": "Cron jobs for your node", "version": "4.3.3", "author": "<PERSON> <<EMAIL>> (https://github.com/ncb000gt)", "bugs": {"url": "https://github.com/kelektiv/node-cron/issues"}, "repository": {"type": "git", "url": "https://github.com/kelektiv/node-cron.git"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -b tsconfig.build.json", "lint:eslint": "eslint src/ tests/", "lint:prettier": "prettier ./**/*.{json,md,yml} --check", "lint": "npm run lint:eslint && npm run lint:prettier", "lint:fix": "npm run lint:eslint -- --fix && npm run lint:prettier -- --write", "test": "cross-env TZ='Europe/Paris' jest --coverage", "test:watch": "cross-env TZ='Europe/Paris' jest --watch --coverage", "test:fuzz": "cross-env TZ='Europe/Paris' jest --testMatch='**/*.fuzz.ts' --coverage=false --testTimeout=120000", "prepare": "husky"}, "dependencies": {"@types/luxon": "~3.7.0", "luxon": "~3.7.0"}, "devDependencies": {"@commitlint/cli": "19.8.1", "@eslint/js": "9.31.0", "@fast-check/jest": "2.1.1", "@insurgent/commitlint-config": "20.0.0", "@insurgent/conventional-changelog-preset": "10.0.0", "@semantic-release/changelog": "6.0.3", "@semantic-release/commit-analyzer": "13.0.1", "@semantic-release/git": "10.0.1", "@semantic-release/github": "11.0.3", "@semantic-release/npm": "12.0.2", "@semantic-release/release-notes-generator": "14.0.3", "@swc/core": "1.13.2", "@swc/jest": "0.2.39", "@types/jest": "29.5.14", "@types/node": "22.16.5", "@types/sinon": "17.0.4", "chai": "5.2.1", "cross-env": "7.0.3", "eslint": "8.57.1", "eslint-config-prettier": "9.1.2", "eslint-plugin-jest": "27.9.0", "eslint-plugin-prettier": "5.5.3", "husky": "9.1.7", "jest": "29.7.0", "lint-staged": "15.5.2", "prettier": "3.6.2", "semantic-release": "24.2.7", "sinon": "20.0.0", "typescript": "5.8.3", "typescript-eslint": "7.18.0"}, "keywords": ["cron", "node cron", "node-cron", "schedule", "scheduler", "cronjob", "cron job"], "license": "MIT", "contributors": ["<PERSON> <https://interlucid.com/contact/> (https://github.com/intcreator)", "<PERSON> <<EMAIL>> (https://github.com/sheerlox)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/toots)", "<PERSON> <> (https://github.com/james<PERSON><PERSON>ey)", "<PERSON> <<EMAIL>> (https://github.com/ErrorProne)", "<PERSON> <<EMAIL>> (https://github.com/cliftonc)", "<PERSON> <<EMAIL>> (https://github.com/neyric)", "humanchimp <<EMAIL>> (https://github.com/humanchimp)", "<PERSON> <<EMAIL>> (https://github.com/spiceapps)", "<PERSON> <<EMAIL>> (https://github.com/danhbear)", "<PERSON><PERSON><PERSON> <vadi<PERSON><EMAIL>> (https://github.com/baryshev)", "<PERSON>nd<PERSON> Ferrari <<EMAIL>> (https://github.com/lfthomaz)", "<PERSON> <<EMAIL>> (https://github.com/greggzigler)", "<PERSON> <<EMAIL>> (https://github.com/jordanabderrachid)", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (matsukaz)", "<PERSON> <<EMAIL>> (https://github.com/kirisu)"], "engines": {"node": ">=18.x"}, "files": ["dist/**/*.js", "dist/**/*.d.ts", "CHANGELOG.md", "LICENSE", "README.md"], "lint-staged": {"*.ts": "eslint src/ tests/ --fix", "*.{json,md,yml}": "prettier ./**/*.{json,md,yml} --check --write"}}