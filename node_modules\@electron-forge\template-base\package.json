{"name": "@electron-forge/template-base", "version": "7.9.0", "description": "Base template for Electron Forge", "repository": "https://github.com/electron/forge", "author": "<PERSON>", "license": "MIT", "main": "dist/BaseTemplate.js", "typings": "dist/BaseTemplate.d.ts", "engines": {"node": ">= 16.4.0"}, "dependencies": {"@electron-forge/core-utils": "7.9.0", "@electron-forge/shared-types": "7.9.0", "@malept/cross-spawn-promise": "^2.0.0", "debug": "^4.3.1", "fs-extra": "^10.0.0", "semver": "^7.2.1", "username": "^5.1.0"}, "devDependencies": {"@electron-forge/test-utils": "7.9.0", "vitest": "^3.1.3"}, "publishConfig": {"access": "public"}, "files": ["dist", "src", "tmpl"], "gitHead": "cd63f57bd6870af2ad847076a183456221b30269"}