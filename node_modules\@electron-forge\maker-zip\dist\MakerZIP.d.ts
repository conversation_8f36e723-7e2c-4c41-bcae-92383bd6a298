import { MakerBase, MakerOptions } from '@electron-forge/maker-base';
import { ForgePlatform } from '@electron-forge/shared-types';
import { MakerZIPConfig } from './Config';
export default class MakerZIP extends MakerBase<MakerZIPConfig> {
    name: string;
    defaultPlatforms: ForgePlatform[];
    isSupportedOnCurrentPlatform(): boolean;
    make({ dir, makeDir, appName, packageJSON, targetArch, targetPlatform, }: MakerOptions): Promise<string[]>;
}
export { MakerZIP, MakerZIPConfig };
//# sourceMappingURL=MakerZIP.d.ts.map