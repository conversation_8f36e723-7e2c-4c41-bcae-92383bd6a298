"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateUpgradedForgeDevDeps = void 0;
const node_path_1 = __importDefault(require("node:path"));
const init_npm_1 = require("../api/init-scripts/init-npm");
function mapMakeTargets(forge5Config) {
    const makeTargets = new Map();
    if (forge5Config.make_targets) {
        for (const [platform, targets] of Object.entries(forge5Config.make_targets)) {
            for (const target of targets) {
                let platforms = makeTargets.get(target);
                if (platforms === undefined) {
                    platforms = [];
                    makeTargets.set(target, platforms);
                }
                platforms.push(platform);
            }
        }
    }
    return makeTargets;
}
const forge5MakerMappings = new Map([
    ['electronInstallerDebian', 'deb'],
    ['electronInstallerDMG', 'dmg'],
    ['electronInstallerFlatpak', 'flatpak'],
    ['electronInstallerRedhat', 'rpm'],
    ['electronInstallerSnap', 'snap'],
    ['electronWinstallerConfig', 'squirrel'],
    ['electronWixMSIConfig', 'wix'],
    ['windowsStoreConfig', 'appx'],
]);
/**
 * Converts Forge v5 maker config to v6.
 */
function generateForgeMakerConfig(forge5Config) {
    const makeTargets = mapMakeTargets(forge5Config);
    const makers = [];
    for (const [forge5Key, makerType] of forge5MakerMappings) {
        const config = forge5Config[forge5Key];
        if (config) {
            makers.push({
                name: `@electron-forge/maker-${makerType}`,
                config: forge5Config[forge5Key],
                platforms: makeTargets.get(makerType) || [],
            });
        }
    }
    const zipPlatforms = makeTargets.get('zip');
    if (zipPlatforms) {
        makers.push({
            name: '@electron-forge/maker-zip',
            platforms: zipPlatforms,
        });
    }
    return makers;
}
const forge5PublisherMappings = new Map([
    ['github_repository', 'github'],
    ['s3', 's3'],
    ['electron-release-server', 'electron-release-server'],
    ['snapStore', 'snapcraft'],
]);
/**
 * Transforms v5 GitHub publisher config to v6 syntax.
 */
function transformGitHubPublisherConfig(config) {
    const { name, owner, options, ...gitHubConfig } = config;
    gitHubConfig.repository = { name, owner };
    if (options) {
        gitHubConfig.octokitOptions = options;
    }
    return gitHubConfig;
}
/**
 * Converts Forge v5 publisher config to v6.
 */
function generateForgePublisherConfig(forge5Config) {
    const publishers = [];
    for (const [forge5Key, publisherType] of forge5PublisherMappings) {
        let config = forge5Config[forge5Key];
        if (config) {
            if (publisherType === 'github') {
                config = transformGitHubPublisherConfig(config);
            }
            publishers.push({
                config,
                name: `@electron-forge/publisher-${publisherType}`,
                platforms: null,
            });
        }
    }
    return publishers;
}
/**
 * Upgrades Forge v5 config to v6.
 */
function upgradeForgeConfig(forge5Config) {
    const forgeConfig = {};
    if (forge5Config.electronPackagerConfig) {
        delete forge5Config.electronPackagerConfig.packageManager;
        forgeConfig.packagerConfig = forge5Config.electronPackagerConfig;
    }
    if (forge5Config.electronRebuildConfig) {
        forgeConfig.rebuildConfig = forge5Config.electronRebuildConfig;
    }
    forgeConfig.makers = generateForgeMakerConfig(forge5Config);
    forgeConfig.publishers = generateForgePublisherConfig(forge5Config);
    return forgeConfig;
}
exports.default = upgradeForgeConfig;
function updateUpgradedForgeDevDeps(packageJSON, devDeps) {
    const forgeConfig = packageJSON.config.forge;
    devDeps = devDeps.filter((dep) => !dep.startsWith('@electron-forge/maker-'));
    devDeps = devDeps.concat(forgeConfig.makers.map((maker) => (0, init_npm_1.siblingDep)(node_path_1.default.basename(maker.name))));
    devDeps = devDeps.concat(forgeConfig.publishers.map((publisher) => (0, init_npm_1.siblingDep)(node_path_1.default.basename(publisher.name))));
    return devDeps;
}
exports.updateUpgradedForgeDevDeps = updateUpgradedForgeDevDeps;
//# sourceMappingURL=data:application/json;base64,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