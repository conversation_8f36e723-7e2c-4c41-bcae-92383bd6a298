{"name": "@electron-forge/maker-rpm", "version": "7.9.0", "description": "Rpm maker for Electron Forge", "repository": "https://github.com/electron/forge", "author": "<PERSON>", "license": "MIT", "main": "dist/MakerRpm.js", "typings": "dist/MakerRpm.d.ts", "devDependencies": {"@electron-forge/test-utils": "7.9.0", "vitest": "^3.1.3"}, "engines": {"node": ">= 16.4.0"}, "dependencies": {"@electron-forge/maker-base": "7.9.0", "@electron-forge/shared-types": "7.9.0"}, "optionalDependencies": {"electron-installer-redhat": "^3.2.0"}, "publishConfig": {"access": "public"}, "files": ["dist", "src"], "gitHead": "cd63f57bd6870af2ad847076a183456221b30269"}