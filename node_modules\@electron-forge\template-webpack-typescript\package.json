{"name": "@electron-forge/template-webpack-typescript", "version": "7.9.0", "description": "Webpack-TypeScript template for Electron Forge", "repository": "https://github.com/electron/forge", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "main": "dist/WebpackTypeScriptTemplate.js", "typings": "dist/WebpackTypeScriptTemplate.d.ts", "engines": {"node": ">= 16.4.0"}, "dependencies": {"@electron-forge/shared-types": "7.9.0", "@electron-forge/template-base": "7.9.0", "fs-extra": "^10.0.0"}, "devDependencies": {"@electron-forge/core-utils": "7.9.0", "@electron-forge/maker-deb": "7.9.0", "@electron-forge/maker-rpm": "7.9.0", "@electron-forge/maker-squirrel": "7.9.0", "@electron-forge/maker-zip": "7.9.0", "@electron-forge/plugin-webpack": "7.9.0", "@electron-forge/test-utils": "7.9.0", "fast-glob": "^3.2.7", "fork-ts-checker-webpack-plugin": "^7.2.13", "listr2": "^7.0.2", "vitest": "^3.1.3"}, "files": ["dist", "src", "tmpl"], "gitHead": "cd63f57bd6870af2ad847076a183456221b30269"}