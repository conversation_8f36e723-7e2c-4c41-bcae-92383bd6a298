import { app, BrowserWindow, ipc<PERSON>ain } from "electron";
import started from "electron-squirrel-startup";
import path from "node:path";
import { InvoiceService } from "./services/InvoiceService";
import { PrinterService } from "./services/PrinterService";
import { StorageService } from "./services/StorageService";

// <PERSON>le creating/removing shortcuts on Windows when installing/uninstalling.
if (started) {
  app.quit();
}

const createWindow = () => {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      preload: path.join(__dirname, "preload.js"),
      contextIsolation: true,
      nodeIntegration: false,
    },
  });

  // and load the index.html of the app.
  if (MAIN_WINDOW_VITE_DEV_SERVER_URL) {
    mainWindow.loadURL(MAIN_WINDOW_VITE_DEV_SERVER_URL);
  } else {
    mainWindow.loadFile(
      path.join(__dirname, `../renderer/${MAIN_WINDOW_VITE_NAME}/index.html`)
    );
  }

  // Open the DevTools.
  mainWindow.webContents.openDevTools();
};

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.on("ready", createWindow);

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
  }
});

app.on("activate", () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and import them here.

// Initialize services
let printerService: PrinterService;
let invoiceService: InvoiceService;
let storageService: StorageService;

// Initialize services when app is ready
app.whenReady().then(() => {
  storageService = new StorageService();
  printerService = new PrinterService(storageService);
  invoiceService = new InvoiceService(storageService, printerService);

  setupIpcHandlers();
});

function setupIpcHandlers() {
  // Printer operations
  ipcMain.handle("get-printers", async () => {
    return await printerService.getPrinters();
  });

  ipcMain.handle("select-printer", async (event, printerId: string) => {
    return await printerService.selectPrinter(printerId);
  });

  ipcMain.handle("get-selected-printer", async () => {
    return await printerService.getSelectedPrinter();
  });

  // Invoice operations
  ipcMain.handle("start-invoice-polling", async () => {
    return await invoiceService.startPolling();
  });

  ipcMain.handle("stop-invoice-polling", async () => {
    return await invoiceService.stopPolling();
  });

  ipcMain.handle("get-invoice-queue", async () => {
    return await invoiceService.getQueue();
  });

  ipcMain.handle("retry-failed-invoices", async () => {
    return await invoiceService.retryFailedInvoices();
  });

  // Status operations
  ipcMain.handle("get-print-server-status", async () => {
    return {
      printerStatus: await printerService.getStatus(),
      invoiceStatus: await invoiceService.getStatus(),
      queueLength: (await invoiceService.getQueue()).length,
    };
  });
}
