import Store from 'electron-store';
import { v4 as uuidv4 } from 'uuid';

export interface Invoice {
  id: string;
  invoiceNumber: string;
  data: any;
  status: 'pending' | 'printing' | 'printed' | 'failed';
  attempts: number;
  createdAt: Date;
  lastAttemptAt?: Date;
  printedAt?: Date;
  error?: string;
}

export interface PrinterConfig {
  selectedPrinterId?: string;
  printerSettings?: any;
}

export interface AppSettings {
  apiEndpoint: string;
  pollingInterval: number;
  maxRetryAttempts: number;
  retryDelay: number;
}

export class StorageService {
  private store: Store;
  private invoiceStore: Store;

  constructor() {
    // Main configuration store
    this.store = new Store({
      name: 'print-server-config',
      defaults: {
        printer: {
          selectedPrinterId: null,
          printerSettings: {}
        } as PrinterConfig,
        settings: {
          apiEndpoint: 'http://localhost:3000/api/invoices',
          pollingInterval: 60000, // 1 minute
          maxRetryAttempts: 3,
          retryDelay: 5000 // 5 seconds
        } as AppSettings
      }
    });

    // Separate store for invoice queue (can grow large)
    this.invoiceStore = new Store({
      name: 'invoice-queue',
      defaults: {
        invoices: [] as Invoice[]
      }
    });
  }

  // Printer configuration methods
  getSelectedPrinter(): string | null {
    return this.store.get('printer.selectedPrinterId') as string | null;
  }

  setSelectedPrinter(printerId: string): void {
    this.store.set('printer.selectedPrinterId', printerId);
  }

  getPrinterSettings(): any {
    return this.store.get('printer.printerSettings');
  }

  setPrinterSettings(settings: any): void {
    this.store.set('printer.printerSettings', settings);
  }

  // App settings methods
  getSettings(): AppSettings {
    return this.store.get('settings') as AppSettings;
  }

  updateSettings(settings: Partial<AppSettings>): void {
    const currentSettings = this.getSettings();
    this.store.set('settings', { ...currentSettings, ...settings });
  }

  // Invoice queue methods
  getAllInvoices(): Invoice[] {
    return this.invoiceStore.get('invoices') as Invoice[];
  }

  addInvoice(invoiceData: any): Invoice {
    const invoice: Invoice = {
      id: uuidv4(),
      invoiceNumber: invoiceData.invoiceNumber || `INV-${Date.now()}`,
      data: invoiceData,
      status: 'pending',
      attempts: 0,
      createdAt: new Date()
    };

    const invoices = this.getAllInvoices();
    invoices.push(invoice);
    this.invoiceStore.set('invoices', invoices);
    
    return invoice;
  }

  updateInvoice(invoiceId: string, updates: Partial<Invoice>): boolean {
    const invoices = this.getAllInvoices();
    const index = invoices.findIndex(inv => inv.id === invoiceId);
    
    if (index === -1) {
      return false;
    }

    invoices[index] = { ...invoices[index], ...updates };
    this.invoiceStore.set('invoices', invoices);
    return true;
  }

  getInvoice(invoiceId: string): Invoice | null {
    const invoices = this.getAllInvoices();
    return invoices.find(inv => inv.id === invoiceId) || null;
  }

  getPendingInvoices(): Invoice[] {
    return this.getAllInvoices().filter(inv => inv.status === 'pending');
  }

  getFailedInvoices(): Invoice[] {
    return this.getAllInvoices().filter(inv => inv.status === 'failed');
  }

  markInvoiceAsPrinting(invoiceId: string): boolean {
    return this.updateInvoice(invoiceId, {
      status: 'printing',
      lastAttemptAt: new Date()
    });
  }

  markInvoiceAsPrinted(invoiceId: string): boolean {
    return this.updateInvoice(invoiceId, {
      status: 'printed',
      printedAt: new Date()
    });
  }

  markInvoiceAsFailed(invoiceId: string, error: string): boolean {
    const invoice = this.getInvoice(invoiceId);
    if (!invoice) return false;

    const attempts = invoice.attempts + 1;
    const maxAttempts = this.getSettings().maxRetryAttempts;

    return this.updateInvoice(invoiceId, {
      status: attempts >= maxAttempts ? 'failed' : 'pending',
      attempts,
      lastAttemptAt: new Date(),
      error
    });
  }

  // Check if invoice already exists (prevent duplicates)
  invoiceExists(invoiceNumber: string): boolean {
    const invoices = this.getAllInvoices();
    return invoices.some(inv => inv.invoiceNumber === invoiceNumber);
  }

  // Cleanup old printed invoices (optional, for maintenance)
  cleanupOldInvoices(daysOld: number = 30): number {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    const invoices = this.getAllInvoices();
    const filteredInvoices = invoices.filter(inv => {
      if (inv.status === 'printed' && inv.printedAt) {
        return new Date(inv.printedAt) > cutoffDate;
      }
      return true; // Keep non-printed invoices
    });

    const removedCount = invoices.length - filteredInvoices.length;
    this.invoiceStore.set('invoices', filteredInvoices);
    
    return removedCount;
  }

  // Get statistics
  getStatistics() {
    const invoices = this.getAllInvoices();
    return {
      total: invoices.length,
      pending: invoices.filter(inv => inv.status === 'pending').length,
      printing: invoices.filter(inv => inv.status === 'printing').length,
      printed: invoices.filter(inv => inv.status === 'printed').length,
      failed: invoices.filter(inv => inv.status === 'failed').length
    };
  }
}
