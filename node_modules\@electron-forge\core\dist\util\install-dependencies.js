"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DepVersionRestriction = exports.DepType = void 0;
const core_utils_1 = require("@electron-forge/core-utils");
const cross_spawn_promise_1 = require("@malept/cross-spawn-promise");
const debug_1 = __importDefault(require("debug"));
const d = (0, debug_1.default)('electron-forge:dependency-installer');
var DepType;
(function (DepType) {
    DepType["PROD"] = "PROD";
    DepType["DEV"] = "DEV";
})(DepType || (exports.DepType = DepType = {}));
var DepVersionRestriction;
(function (DepVersionRestriction) {
    DepVersionRestriction["EXACT"] = "EXACT";
    DepVersionRestriction["RANGE"] = "RANGE";
})(DepVersionRestriction || (exports.DepVersionRestriction = DepVersionRestriction = {}));
exports.default = async (pm, dir, deps, depType = DepType.PROD, versionRestriction = DepVersionRestriction.RANGE) => {
    d('installing', JSON.stringify(deps), 'in:', dir, `depType=${depType},versionRestriction=${versionRestriction},withPackageManager=${pm.executable}`);
    if (deps.length === 0) {
        d('nothing to install, stopping immediately');
        return Promise.resolve();
    }
    const cmd = [pm.install].concat(deps);
    if (depType === DepType.DEV)
        cmd.push(pm.dev);
    if (versionRestriction === DepVersionRestriction.EXACT)
        cmd.push(pm.exact);
    d('executing', JSON.stringify(cmd), 'in:', dir);
    try {
        await (0, core_utils_1.spawnPackageManager)(pm, cmd, {
            cwd: dir,
            stdio: 'pipe',
        });
    }
    catch (err) {
        if (err instanceof cross_spawn_promise_1.ExitError) {
            throw new Error(`Failed to install modules: ${JSON.stringify(deps)}\n\nWith output: ${err.message}\n${err.stderr ? err.stderr.toString() : ''}`);
        }
        else {
            throw err;
        }
    }
};
//# sourceMappingURL=data:application/json;base64,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