#!/usr/bin/env node
"use strict";
// This file requires a shebang above. If it is missing, this is an error.
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const chalk_1 = __importDefault(require("chalk"));
const commander_1 = require("commander");
const log_symbols_1 = __importDefault(require("log-symbols"));
const semver_1 = __importDefault(require("semver"));
const package_json_1 = __importDefault(require("../package.json"));
require("./util/terminate");
const check_system_1 = require("./util/check-system");
if (!semver_1.default.satisfies(process.versions.node, package_json_1.default.engines.node)) {
    console.error(log_symbols_1.default.error, `You are running Node.js version ${chalk_1.default.red(process.versions.node)}, but Electron Forge requires Node.js ${chalk_1.default.red(package_json_1.default.engines.node)}. \n`);
    process.exit(1);
}
/* eslint-disable-next-line import/order -- Listr2 import contains JS syntax that fails as early as Node 14 */
const listr2_1 = require("listr2");
commander_1.program
    .version(package_json_1.default.version, '-V, --version', 'Output the current version.')
    .helpOption('-h, --help', 'Output usage information.')
    .command('init', 'Initialize a new Electron application.')
    .command('import', 'Import an existing Electron project to Forge.')
    .command('start', 'Start the current Electron application in development mode.')
    .command('package', 'Package the current Electron application.')
    .command('make', 'Generate distributables for the current Electron application.')
    .command('publish', 'Publish the current Electron application.')
    .passThroughOptions(true)
    .hook('preSubcommand', async (_command, subcommand) => {
    if (!process.argv.includes('--help') && !process.argv.includes('-h')) {
        const runner = new listr2_1.Listr([
            {
                title: 'Checking your system',
                task: async (ctx, task) => {
                    ctx.command = subcommand.name();
                    ctx.git = !process.argv.includes('--skip-git');
                    return await (0, check_system_1.checkSystem)(task);
                },
            },
        ], {
            concurrent: false,
            exitOnError: true,
            fallbackRendererCondition: Boolean(process.env.DEBUG) || Boolean(process.env.CI),
        });
        try {
            await runner.run();
        }
        catch {
            process.exit(1);
        }
    }
});
commander_1.program.parse(process.argv);
//# sourceMappingURL=data:application/json;base64,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