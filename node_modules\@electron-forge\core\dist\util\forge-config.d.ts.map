{"version": 3, "file": "forge-config.d.ts", "sourceRoot": "", "sources": ["../../src/util/forge-config.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,mBAAmB,EAAE,MAAM,8BAA8B,CAAC;AAoGhF,eAAO,MAAM,sBAAsB,EAAE,GAAG,CAAC,MAAM,EAAE,WAAW,CAAa,CAAC;AAC1E,wBAAgB,+BAA+B,CAC7C,GAAG,EAAE,MAAM,EACX,MAAM,EAAE,WAAW,GAClB,IAAI,CAEN;AACD,wBAAgB,iCAAiC,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI,CAEnE;AAED,MAAM,MAAM,kBAAkB,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;AAClE,MAAM,MAAM,qBAAqB,CAAC,CAAC,IAAI;IACrC,GAAG,EAAE,kBAAkB,CAAC,CAAC,CAAC,CAAC;IAC3B,2BAA2B,EAAE,IAAI,CAAC;CACnC,CAAC;AAEF,wBAAgB,mBAAmB,CAAC,CAAC,EACnC,GAAG,EAAE,kBAAkB,CAAC,CAAC,CAAC,GACzB,qBAAqB,CAAC,CAAC,CAAC,CAK1B;AAED,wBAAsB,0BAA0B,CAC9C,GAAG,EAAE,MAAM,EACX,WAAW,EAAE,MAAM,GAAG,WAAW,GAChC,OAAO,CAAC,OAAO,CAAC,CAMlB;AAGD,wBAAgB,oBAAoB,CAClC,GAAG,EAAE,MAAM,EACX,WAAW,EAAE,GAAG,EAChB,GAAG,EAAE,GAAG,GACP,IAAI,CAYN;8BAK0B,MAAM,KAAG,QAAQ,mBAAmB,CAAC;AAAhE,wBA6FE"}