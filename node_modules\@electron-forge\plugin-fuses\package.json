{"name": "@electron-forge/plugin-fuses", "version": "7.9.0", "description": "A plugin for flipping Electron Fuses in Electron Forge", "repository": "https://github.com/electron/forge", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "main": "dist/FusesPlugin.js", "files": ["dist", "src", "package.json", "README.md"], "typings": "dist/FusesPlugin.d.ts", "devDependencies": {"@electron/fuses": "^1.0.0", "@malept/cross-spawn-promise": "^2.0.0", "xvfb-maybe": "^0.2.1"}, "peerDependencies": {"@electron/fuses": "^1.0.0"}, "engines": {"node": ">= 16.4.0"}, "dependencies": {"@electron-forge/plugin-base": "7.9.0", "@electron-forge/shared-types": "7.9.0"}, "publishConfig": {"access": "public"}, "gitHead": "cd63f57bd6870af2ad847076a183456221b30269"}