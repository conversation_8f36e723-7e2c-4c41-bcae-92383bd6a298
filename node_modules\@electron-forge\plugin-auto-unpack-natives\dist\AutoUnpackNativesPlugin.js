"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutoUnpackNativesPlugin = void 0;
const plugin_base_1 = require("@electron-forge/plugin-base");
class AutoUnpackNativesPlugin extends plugin_base_1.PluginBase {
    constructor() {
        super(...arguments);
        this.name = 'auto-unpack-natives';
        this.resolveForgeConfig = async (forgeConfig) => {
            if (!forgeConfig.packagerConfig) {
                forgeConfig.packagerConfig = {};
            }
            if (!forgeConfig.packagerConfig.asar) {
                throw new Error('The AutoUnpackNatives plugin requires asar to be truthy or an object');
            }
            if (forgeConfig.packagerConfig.asar === true) {
                forgeConfig.packagerConfig.asar = {};
            }
            const existingUnpack = forgeConfig.packagerConfig.asar.unpack;
            const newUnpack = '**/{.**,**}/**/*.node';
            if (existingUnpack) {
                forgeConfig.packagerConfig.asar.unpack = `{${existingUnpack},${newUnpack}}`;
            }
            else {
                forgeConfig.packagerConfig.asar.unpack = newUnpack;
            }
            return forgeConfig;
        };
    }
    getHooks() {
        return {
            resolveForgeConfig: this.resolveForgeConfig,
        };
    }
}
exports.default = AutoUnpackNativesPlugin;
exports.AutoUnpackNativesPlugin = AutoUnpackNativesPlugin;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiQXV0b1VucGFja05hdGl2ZXNQbHVnaW4uanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi9zcmMvQXV0b1VucGFja05hdGl2ZXNQbHVnaW4udHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7O0FBQUEsNkRBQXlEO0FBS3pELE1BQXFCLHVCQUF3QixTQUFRLHdCQUFtQztJQUF4Rjs7UUFDRSxTQUFJLEdBQUcscUJBQXFCLENBQUM7UUFRN0IsdUJBQWtCLEdBQXNDLEtBQUssRUFDM0QsV0FBVyxFQUNYLEVBQUU7WUFDRixJQUFJLENBQUMsV0FBVyxDQUFDLGNBQWMsRUFBRSxDQUFDO2dCQUNoQyxXQUFXLENBQUMsY0FBYyxHQUFHLEVBQUUsQ0FBQztZQUNsQyxDQUFDO1lBQ0QsSUFBSSxDQUFDLFdBQVcsQ0FBQyxjQUFjLENBQUMsSUFBSSxFQUFFLENBQUM7Z0JBQ3JDLE1BQU0sSUFBSSxLQUFLLENBQ2Isc0VBQXNFLENBQ3ZFLENBQUM7WUFDSixDQUFDO1lBQ0QsSUFBSSxXQUFXLENBQUMsY0FBYyxDQUFDLElBQUksS0FBSyxJQUFJLEVBQUUsQ0FBQztnQkFDN0MsV0FBVyxDQUFDLGNBQWMsQ0FBQyxJQUFJLEdBQUcsRUFBRSxDQUFDO1lBQ3ZDLENBQUM7WUFDRCxNQUFNLGNBQWMsR0FBRyxXQUFXLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUM7WUFDOUQsTUFBTSxTQUFTLEdBQUcsdUJBQXVCLENBQUM7WUFDMUMsSUFBSSxjQUFjLEVBQUUsQ0FBQztnQkFDbkIsV0FBVyxDQUFDLGNBQWMsQ0FBQyxJQUFJLENBQUMsTUFBTSxHQUFHLElBQUksY0FBYyxJQUFJLFNBQVMsR0FBRyxDQUFDO1lBQzlFLENBQUM7aUJBQU0sQ0FBQztnQkFDTixXQUFXLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxNQUFNLEdBQUcsU0FBUyxDQUFDO1lBQ3JELENBQUM7WUFDRCxPQUFPLFdBQVcsQ0FBQztRQUNyQixDQUFDLENBQUM7SUFDSixDQUFDO0lBN0JDLFFBQVE7UUFDTixPQUFPO1lBQ0wsa0JBQWtCLEVBQUUsSUFBSSxDQUFDLGtCQUFrQjtTQUM1QyxDQUFDO0lBQ0osQ0FBQztDQXlCRjtBQWhDRCwwQ0FnQ0M7QUFFUSwwREFBdUIifQ==