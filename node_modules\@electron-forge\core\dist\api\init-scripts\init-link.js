"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.initLink = void 0;
const node_fs_1 = __importDefault(require("node:fs"));
const node_path_1 = __importDefault(require("node:path"));
const core_utils_1 = require("@electron-forge/core-utils");
const debug_1 = __importDefault(require("debug"));
const read_package_json_1 = require("../../util/read-package-json");
const d = (0, debug_1.default)('electron-forge:init:link');
/**
 * Link local forge dependencies
 *
 * This allows developers working on forge itself to easily init
 * a local template and have it use their local plugins / core / cli packages.
 *
 * Note: `yarn link:prepare` needs to run first before dependencies can be
 * linked.
 */
async function initLink(pm, dir, task) {
    const shouldLink = process.env.LINK_FORGE_DEPENDENCIES_ON_INIT;
    if (shouldLink) {
        d('Linking forge dependencies');
        const packageJson = await (0, read_package_json_1.readRawPackageJson)(dir);
        // TODO(erickzhao): the `--link-folder` argument only works for `yarn`. Since this command is
        // only made for Forge contributors, it isn't a big deal if it doesn't work for other package managers,
        // but we should make it cleaner.
        const linkFolder = node_path_1.default.resolve(__dirname, '..', '..', '..', '..', '..', '..', '.links');
        for (const packageName of Object.keys(packageJson.devDependencies)) {
            if (packageName.startsWith('@electron-forge/')) {
                if (task)
                    task.output = `${pm.executable} link --link-folder ${linkFolder} ${packageName}`;
                await (0, core_utils_1.spawnPackageManager)(pm, ['link', '--link-folder', linkFolder, packageName], {
                    cwd: dir,
                });
            }
        }
        await node_fs_1.default.promises.chmod(node_path_1.default.resolve(dir, 'node_modules', '.bin', 'electron-forge'), 0o755);
    }
    else {
        d('LINK_FORGE_DEPENDENCIES_ON_INIT is falsy. Skipping.');
    }
}
exports.initLink = initLink;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5pdC1saW5rLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vc3JjL2FwaS9pbml0LXNjcmlwdHMvaW5pdC1saW5rLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLHNEQUF5QjtBQUN6QiwwREFBNkI7QUFFN0IsMkRBQTRFO0FBRTVFLGtEQUEwQjtBQUUxQixvRUFBa0U7QUFFbEUsTUFBTSxDQUFDLEdBQUcsSUFBQSxlQUFLLEVBQUMsMEJBQTBCLENBQUMsQ0FBQztBQUU1Qzs7Ozs7Ozs7R0FRRztBQUNJLEtBQUssVUFBVSxRQUFRLENBQzVCLEVBQWEsRUFDYixHQUFXLEVBQ1gsSUFBd0I7SUFFeEIsTUFBTSxVQUFVLEdBQUcsT0FBTyxDQUFDLEdBQUcsQ0FBQywrQkFBK0IsQ0FBQztJQUMvRCxJQUFJLFVBQVUsRUFBRSxDQUFDO1FBQ2YsQ0FBQyxDQUFDLDRCQUE0QixDQUFDLENBQUM7UUFDaEMsTUFBTSxXQUFXLEdBQUcsTUFBTSxJQUFBLHNDQUFrQixFQUFDLEdBQUcsQ0FBQyxDQUFDO1FBQ2xELDZGQUE2RjtRQUM3Rix1R0FBdUc7UUFDdkcsaUNBQWlDO1FBQ2pDLE1BQU0sVUFBVSxHQUFHLG1CQUFJLENBQUMsT0FBTyxDQUM3QixTQUFTLEVBQ1QsSUFBSSxFQUNKLElBQUksRUFDSixJQUFJLEVBQ0osSUFBSSxFQUNKLElBQUksRUFDSixJQUFJLEVBQ0osUUFBUSxDQUNULENBQUM7UUFDRixLQUFLLE1BQU0sV0FBVyxJQUFJLE1BQU0sQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLGVBQWUsQ0FBQyxFQUFFLENBQUM7WUFDbkUsSUFBSSxXQUFXLENBQUMsVUFBVSxDQUFDLGtCQUFrQixDQUFDLEVBQUUsQ0FBQztnQkFDL0MsSUFBSSxJQUFJO29CQUNOLElBQUksQ0FBQyxNQUFNLEdBQUcsR0FBRyxFQUFFLENBQUMsVUFBVSx1QkFBdUIsVUFBVSxJQUFJLFdBQVcsRUFBRSxDQUFDO2dCQUNuRixNQUFNLElBQUEsZ0NBQW1CLEVBQ3ZCLEVBQUUsRUFDRixDQUFDLE1BQU0sRUFBRSxlQUFlLEVBQUUsVUFBVSxFQUFFLFdBQVcsQ0FBQyxFQUNsRDtvQkFDRSxHQUFHLEVBQUUsR0FBRztpQkFDVCxDQUNGLENBQUM7WUFDSixDQUFDO1FBQ0gsQ0FBQztRQUNELE1BQU0saUJBQUUsQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUNyQixtQkFBSSxDQUFDLE9BQU8sQ0FBQyxHQUFHLEVBQUUsY0FBYyxFQUFFLE1BQU0sRUFBRSxnQkFBZ0IsQ0FBQyxFQUMzRCxLQUFLLENBQ04sQ0FBQztJQUNKLENBQUM7U0FBTSxDQUFDO1FBQ04sQ0FBQyxDQUFDLHFEQUFxRCxDQUFDLENBQUM7SUFDM0QsQ0FBQztBQUNILENBQUM7QUExQ0QsNEJBMENDIn0=