"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const node_path_1 = __importDefault(require("node:path"));
const core_utils_1 = require("@electron-forge/core-utils");
const chalk_1 = __importDefault(require("chalk"));
const debug_1 = __importDefault(require("debug"));
const listr2_1 = require("listr2");
const semver_1 = __importDefault(require("semver"));
const install_dependencies_1 = __importStar(require("../util/install-dependencies"));
const read_package_json_1 = require("../util/read-package-json");
const find_template_1 = require("./init-scripts/find-template");
const init_directory_1 = require("./init-scripts/init-directory");
const init_git_1 = require("./init-scripts/init-git");
const init_link_1 = require("./init-scripts/init-link");
const init_npm_1 = require("./init-scripts/init-npm");
const d = (0, debug_1.default)('electron-forge:init');
async function validateTemplate(template, templateModule) {
    if (!templateModule.requiredForgeVersion) {
        throw new Error(`Cannot use a template (${template}) with this version of Electron Forge, as it does not specify its required Forge version.`);
    }
    const forgeVersion = (await (0, read_package_json_1.readRawPackageJson)(node_path_1.default.join(__dirname, '..', '..'))).version;
    if (!semver_1.default.satisfies(forgeVersion, templateModule.requiredForgeVersion)) {
        throw new Error(`Template (${template}) is not compatible with this version of Electron Forge (${forgeVersion}), it requires ${templateModule.requiredForgeVersion}`);
    }
}
exports.default = async ({ dir = process.cwd(), interactive = false, copyCIFiles = false, force = false, template = 'base', skipGit = false, }) => {
    d(`Initializing in: ${dir}`);
    const runner = new listr2_1.Listr([
        {
            title: `Resolving package manager`,
            task: async (ctx, task) => {
                ctx.pm = await (0, core_utils_1.resolvePackageManager)();
                task.title = `Resolving package manager: ${chalk_1.default.cyan(ctx.pm.executable)}`;
            },
        },
        {
            title: `Resolving template: ${chalk_1.default.cyan(template)}`,
            task: async (ctx, task) => {
                const tmpl = await (0, find_template_1.findTemplate)(template);
                ctx.templateModule = tmpl.template;
                task.output = `Using ${chalk_1.default.green(tmpl.name)} (${tmpl.type} module)`;
            },
            rendererOptions: { persistentOutput: true },
        },
        {
            title: 'Initializing directory',
            task: async (_, task) => {
                await (0, init_directory_1.initDirectory)(dir, task, force);
            },
            rendererOptions: { persistentOutput: true },
        },
        {
            title: 'Initializing git repository',
            enabled: !skipGit,
            task: async () => {
                await (0, init_git_1.initGit)(dir);
            },
        },
        {
            title: 'Preparing template',
            task: async ({ templateModule }) => {
                await validateTemplate(template, templateModule);
            },
        },
        {
            title: `Initializing template`,
            task: async ({ templateModule }, task) => {
                if (typeof templateModule.initializeTemplate === 'function') {
                    const tasks = await templateModule.initializeTemplate(dir, {
                        copyCIFiles,
                        force,
                    });
                    if (tasks) {
                        return task.newListr(tasks, { concurrent: false });
                    }
                }
            },
        },
        {
            title: 'Installing template dependencies',
            task: async ({ templateModule }, task) => {
                return task.newListr([
                    {
                        title: 'Installing production dependencies',
                        task: async ({ pm }, task) => {
                            d('installing dependencies');
                            if (templateModule.dependencies?.length) {
                                task.output = `${pm.executable} ${pm.install} ${pm.dev} ${templateModule.dependencies.join(' ')}`;
                            }
                            return await (0, install_dependencies_1.default)(pm, dir, templateModule.dependencies || [], install_dependencies_1.DepType.PROD, install_dependencies_1.DepVersionRestriction.RANGE);
                        },
                        exitOnError: false,
                    },
                    {
                        title: 'Installing development dependencies',
                        task: async ({ pm }, task) => {
                            d('installing devDependencies');
                            if (templateModule.devDependencies?.length) {
                                task.output = `${pm.executable} ${pm.install} ${pm.dev} ${templateModule.devDependencies.join(' ')}`;
                            }
                            await (0, install_dependencies_1.default)(pm, dir, templateModule.devDependencies || [], install_dependencies_1.DepType.DEV);
                        },
                        exitOnError: false,
                    },
                    {
                        title: 'Finalizing dependencies',
                        task: async (_, task) => {
                            return task.newListr([
                                {
                                    title: 'Installing common dependencies',
                                    task: async ({ pm }, task) => {
                                        await (0, init_npm_1.initNPM)(pm, dir, task);
                                    },
                                    exitOnError: false,
                                },
                                {
                                    title: 'Linking Forge dependencies to local build',
                                    enabled: !!process.env.LINK_FORGE_DEPENDENCIES_ON_INIT,
                                    task: async ({ pm }, task) => {
                                        await (0, init_link_1.initLink)(pm, dir, task);
                                    },
                                    exitOnError: true,
                                },
                            ]);
                        },
                    },
                ], {
                    concurrent: false,
                });
            },
        },
    ], {
        concurrent: false,
        silentRendererCondition: !interactive,
        fallbackRendererCondition: Boolean(process.env.DEBUG) || Boolean(process.env.CI),
    });
    await runner.run();
};
//# sourceMappingURL=data:application/json;base64,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