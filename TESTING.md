# Testing Guide for Kassierer Print Server

This document outlines how to test the print server functionality to ensure it works correctly.

## Manual Testing Checklist

### 1. Application Startup
- [ ] Application starts without errors
- [ ] UI loads correctly with all sections visible
- [ ] Status indicator shows "Initializing..." then "Ready"
- [ ] No console errors in developer tools

### 2. Printer Discovery and Selection
- [ ] Click "🔄 Refresh Printers" button
- [ ] Verify mock printers appear in dropdown:
  - Microsoft Print to PDF (Default)
  - HP LaserJet Pro M404dn
  - Canon PIXMA TS3300 (Offline)
- [ ] Select "Microsoft Print to PDF"
- [ ] Verify printer details appear below dropdown
- [ ] "🧪 Test Print" button becomes enabled
- [ ] Click "🧪 Test Print" and verify success message

### 3. Print Server Operations
- [ ] Click "▶️ Start Polling" button
- [ ] Verify success message appears
- [ ] Button changes to disabled state
- [ ] "⏹️ Stop Polling" button becomes enabled
- [ ] Status changes to "Polling for invoices..."
- [ ] Status indicator dot turns green

### 4. Queue Management
- [ ] Wait for mock invoices to appear (10% chance every minute)
- [ ] Verify invoices appear in queue with correct status
- [ ] Check that invoice details are displayed correctly
- [ ] Verify status badges show correct colors:
  - Yellow for "pending"
  - Blue for "printing" 
  - Green for "printed"
  - Red for "failed"

### 5. Statistics Updates
- [ ] Verify queue length updates in real-time
- [ ] Check that "Total Printed" counter increases
- [ ] Verify "Failed" counter shows failed prints
- [ ] Statistics refresh every 5 seconds

### 6. Error Handling
- [ ] Select "Canon PIXMA" (offline printer)
- [ ] Start polling and wait for invoice
- [ ] Verify print fails with "Printer is offline" error
- [ ] Check that invoice status changes to "failed"
- [ ] Click "🔄 Retry Failed" and verify retry attempt

### 7. Stop/Start Functionality
- [ ] Click "⏹️ Stop Polling"
- [ ] Verify polling stops and status updates
- [ ] Click "▶️ Start Polling" again
- [ ] Verify polling resumes correctly

## Automated Testing Scenarios

### Test Case 1: Basic Printer Discovery
```typescript
// Test that mock printers are returned correctly
const printers = await window.electronAPI.getPrinters();
expect(printers).toHaveLength(3);
expect(printers[0].name).toBe('Microsoft Print to PDF');
expect(printers[0].isDefault).toBe(true);
```

### Test Case 2: Printer Selection
```typescript
// Test printer selection and persistence
const success = await window.electronAPI.selectPrinter('Microsoft_Print_to_PDF');
expect(success).toBe(true);

const selected = await window.electronAPI.getSelectedPrinter();
expect(selected).toBe('Microsoft_Print_to_PDF');
```

### Test Case 3: Invoice Processing
```typescript
// Test that invoices are processed correctly
await window.electronAPI.startInvoicePolling();
// Wait for mock invoice generation
await new Promise(resolve => setTimeout(resolve, 2000));
const queue = await window.electronAPI.getInvoiceQueue();
expect(queue.length).toBeGreaterThan(0);
```

## Performance Testing

### Memory Usage
- [ ] Monitor memory usage over 30 minutes of operation
- [ ] Verify no memory leaks with continuous polling
- [ ] Check that old printed invoices don't accumulate indefinitely

### Response Times
- [ ] Printer discovery should complete within 2 seconds
- [ ] Invoice processing should start within 1 second of API response
- [ ] UI updates should be immediate (< 100ms)

### Stress Testing
- [ ] Test with 100+ invoices in queue
- [ ] Verify application remains responsive
- [ ] Check that all invoices are processed correctly

## Integration Testing

### API Integration (when real API is available)
- [ ] Configure real API endpoint
- [ ] Verify correct HTTP requests are made
- [ ] Test with various API response formats
- [ ] Handle API errors gracefully

### Printer Integration (when real printers are available)
- [ ] Test with actual network printers
- [ ] Verify PDF generation works correctly
- [ ] Test printing to different printer types
- [ ] Handle printer offline scenarios

## Error Scenarios to Test

### Network Issues
- [ ] Disconnect network during polling
- [ ] Verify graceful error handling
- [ ] Test reconnection behavior

### Printer Issues
- [ ] Remove printer during operation
- [ ] Test with printer out of paper/toner
- [ ] Verify error messages are helpful

### Data Corruption
- [ ] Test with corrupted storage files
- [ ] Verify application recovers gracefully
- [ ] Check data integrity after restart

## Security Testing

### IPC Security
- [ ] Verify renderer cannot access Node.js APIs directly
- [ ] Test that only exposed APIs are accessible
- [ ] Check for potential XSS vulnerabilities

### File System Access
- [ ] Verify temp files are cleaned up
- [ ] Check that storage files have appropriate permissions
- [ ] Test behavior with read-only storage directory

## Regression Testing

After any code changes, verify:
- [ ] All existing functionality still works
- [ ] No new console errors appear
- [ ] Performance hasn't degraded
- [ ] UI remains responsive

## Test Environment Setup

### Development Testing
```bash
# Start with debug logging
DEBUG=kassierer:* npm start

# Run with mock data
NODE_ENV=development npm start
```

### Production Testing
```bash
# Build and test production version
npm run make
# Test the built application
```

## Known Issues and Limitations

### Current Limitations
- Mock printer implementation only
- No real PDF generation (text files only)
- Limited error simulation
- No automated test suite yet

### Future Testing Improvements
- Add Jest/Electron testing framework
- Implement automated UI testing with Spectron
- Add performance benchmarking
- Create CI/CD pipeline with automated testing

## Reporting Issues

When reporting issues, include:
- Steps to reproduce
- Expected vs actual behavior
- Console logs and error messages
- System information (OS, Node version, etc.)
- Screenshots if UI-related

## Test Data

### Sample Invoice Data
```json
{
  "invoiceNumber": "INV-TEST-001",
  "customerName": "Test Customer",
  "amount": 123.45,
  "items": [
    {
      "name": "Test Item",
      "quantity": 2,
      "price": 61.725
    }
  ],
  "date": "2024-01-01T12:00:00Z"
}
```

### Mock Printer Configurations
- **PDF Printer**: Always succeeds, good for basic testing
- **Network Printer**: Simulates real network printer behavior
- **Offline Printer**: Always fails, good for error testing
