"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MakerSquirrel = void 0;
const node_path_1 = __importDefault(require("node:path"));
const maker_base_1 = require("@electron-forge/maker-base");
const electron_winstaller_1 = require("electron-winstaller");
const fs_extra_1 = __importDefault(require("fs-extra"));
class MakerSquirrel extends maker_base_1.MakerBase {
    constructor() {
        super(...arguments);
        this.name = 'squirrel';
        this.defaultPlatforms = ['win32'];
    }
    isSupportedOnCurrentPlatform() {
        return (this.isInstalled('electron-winstaller') &&
            !process.env.DISABLE_SQUIRREL_TEST);
    }
    async make({ dir, makeDir, targetArch, packageJ<PERSON><PERSON>, appName, forgeConfig, }) {
        const outPath = node_path_1.default.resolve(makeDir, `squirrel.windows/${targetArch}`);
        await this.ensureDirectory(outPath);
        const winstallerConfig = {
            name: typeof packageJSON.name === 'string'
                ? packageJSON.name.replace(/-/g, '_')
                : undefined, // squirrel hates hyphens
            title: appName,
            noMsi: true,
            exe: `${forgeConfig.packagerConfig.executableName || appName}.exe`,
            setupExe: `${appName}-${packageJSON.version} Setup.exe`,
            ...this.config,
            appDirectory: dir,
            outputDirectory: outPath,
        };
        await (0, electron_winstaller_1.createWindowsInstaller)(winstallerConfig);
        const nupkgVersion = (0, electron_winstaller_1.convertVersion)(packageJSON.version);
        const artifacts = [
            node_path_1.default.resolve(outPath, 'RELEASES'),
            node_path_1.default.resolve(outPath, winstallerConfig.setupExe || `${appName}Setup.exe`),
            node_path_1.default.resolve(outPath, `${winstallerConfig.name}-${nupkgVersion}-full.nupkg`),
        ];
        const deltaPath = node_path_1.default.resolve(outPath, `${winstallerConfig.name}-${nupkgVersion}-delta.nupkg`);
        if (winstallerConfig.remoteReleases &&
            !winstallerConfig.noDelta &&
            (await fs_extra_1.default.pathExists(deltaPath))) {
            artifacts.push(deltaPath);
        }
        const msiPath = node_path_1.default.resolve(outPath, winstallerConfig.setupMsi || `${appName}Setup.msi`);
        if (!winstallerConfig.noMsi && (await fs_extra_1.default.pathExists(msiPath))) {
            artifacts.push(msiPath);
        }
        return artifacts;
    }
}
exports.default = MakerSquirrel;
exports.MakerSquirrel = MakerSquirrel;
//# sourceMappingURL=data:application/json;base64,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