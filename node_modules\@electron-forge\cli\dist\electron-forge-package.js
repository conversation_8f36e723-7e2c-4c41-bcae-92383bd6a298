"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const get_1 = require("@electron/get");
const core_1 = require("@electron-forge/core");
const commander_1 = require("commander");
require("./util/terminate");
const package_json_1 = __importDefault(require("../package.json"));
const resolve_working_dir_1 = require("./util/resolve-working-dir");
commander_1.program
    .version(package_json_1.default.version, '-V, --version', 'Output the current version')
    .helpOption('-h, --help', 'Output usage information')
    .argument('[dir]', 'Directory to run the command in. (default: current directory)')
    .option('-a, --arch [arch]', 'Target build architecture')
    .option('-p, --platform [platform]', 'Target build platform')
    .action(async (dir) => {
    const workingDir = (0, resolve_working_dir_1.resolveWorkingDir)(dir);
    const options = commander_1.program.opts();
    (0, get_1.initializeProxy)();
    const packageOpts = {
        dir: workingDir,
        interactive: true,
    };
    if (options.arch)
        packageOpts.arch = options.arch;
    if (options.platform)
        packageOpts.platform = options.platform;
    await core_1.api.package(packageOpts);
})
    .parse(process.argv);
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZWxlY3Ryb24tZm9yZ2UtcGFja2FnZS5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uL3NyYy9lbGVjdHJvbi1mb3JnZS1wYWNrYWdlLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsdUNBQWdEO0FBQ2hELCtDQUEyRDtBQUMzRCx5Q0FBb0M7QUFFcEMsNEJBQTBCO0FBQzFCLG1FQUEwQztBQUUxQyxvRUFBK0Q7QUFFL0QsbUJBQU87S0FDSixPQUFPLENBQUMsc0JBQVcsQ0FBQyxPQUFPLEVBQUUsZUFBZSxFQUFFLDRCQUE0QixDQUFDO0tBQzNFLFVBQVUsQ0FBQyxZQUFZLEVBQUUsMEJBQTBCLENBQUM7S0FDcEQsUUFBUSxDQUNQLE9BQU8sRUFDUCwrREFBK0QsQ0FDaEU7S0FDQSxNQUFNLENBQUMsbUJBQW1CLEVBQUUsMkJBQTJCLENBQUM7S0FDeEQsTUFBTSxDQUFDLDJCQUEyQixFQUFFLHVCQUF1QixDQUFDO0tBQzVELE1BQU0sQ0FBQyxLQUFLLEVBQUUsR0FBRyxFQUFFLEVBQUU7SUFDcEIsTUFBTSxVQUFVLEdBQUcsSUFBQSx1Q0FBaUIsRUFBQyxHQUFHLENBQUMsQ0FBQztJQUUxQyxNQUFNLE9BQU8sR0FBRyxtQkFBTyxDQUFDLElBQUksRUFBRSxDQUFDO0lBRS9CLElBQUEscUJBQWUsR0FBRSxDQUFDO0lBRWxCLE1BQU0sV0FBVyxHQUFtQjtRQUNsQyxHQUFHLEVBQUUsVUFBVTtRQUNmLFdBQVcsRUFBRSxJQUFJO0tBQ2xCLENBQUM7SUFDRixJQUFJLE9BQU8sQ0FBQyxJQUFJO1FBQUUsV0FBVyxDQUFDLElBQUksR0FBRyxPQUFPLENBQUMsSUFBSSxDQUFDO0lBQ2xELElBQUksT0FBTyxDQUFDLFFBQVE7UUFBRSxXQUFXLENBQUMsUUFBUSxHQUFHLE9BQU8sQ0FBQyxRQUFRLENBQUM7SUFFOUQsTUFBTSxVQUFHLENBQUMsT0FBTyxDQUFDLFdBQVcsQ0FBQyxDQUFDO0FBQ2pDLENBQUMsQ0FBQztLQUNELEtBQUssQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLENBQUMifQ==